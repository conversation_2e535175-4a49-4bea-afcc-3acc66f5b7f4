// Created by carolsail
import UIbase from '../utils/UIbase';
import LocalData from '../manager/LocalData';
import PrefabUtil from '../utils/manager/PrefabUtil';
import AudioMgr from '../manager/AudioMgr';
import AudioPath from '../datas/AudioPath';
import GameUI from './GameUI';
import HomeUI from './HomeUI';
import Plat from '../utils/Palt';

const { ccclass, property } = cc._decorator;

@ccclass
export default class PauseUI extends UIbase {

    @property(cc.Node)
    autoPlayPanel: cc.Node = null; // 自动玩面板

    @property(cc.Node)
    autoPlayCheckbox: cc.Node = null; // 勾选框

    @property(cc.Node)
    autoPlayCheckMark: cc.Node = null; // 勾选标记

    @property(cc.Label)
    autoPlayLabel: cc.Label = null; // 自动玩文字

    private static _inst:PauseUI;

    public static get inst()
    {
        if(this._inst==null  || this._inst.node==null)
        {
            let prefab = PrefabUtil.get("PauseUI");
            if (!prefab) {
                console.error("PauseUI预制体未找到");
                return null;
            }
            let v=cc.instantiate(prefab);

            this._inst=v.getComponent(PauseUI);
        }

        return this._inst;
    }

    start() {
        this.initAutoPlayUI();

        // 监听VIP状态变化
        cc.systemEvent.on('vipStatusChanged', this.onVipStatusChanged, this);
    }

    protected onShow(): void {
        Plat.showBanner()
        this.updateAutoPlayUI();
        this.updateVipUI();
    }

    // 初始化自动玩UI
    private initAutoPlayUI() {
        if (this.autoPlayLabel) {
            this.autoPlayLabel.string = "是否开启自动玩";
        }

        // 绑定勾选框点击事件
        if (this.autoPlayCheckbox) {
            this.autoPlayCheckbox.on(cc.Node.EventType.TOUCH_END, this.onClickAutoPlayCheckbox, this);
        }

        // 确保CheckMark初始状态为隐藏（Sprite组件）
        if (this.autoPlayCheckMark) {
            this.autoPlayCheckMark.active = false;
        }
    }

    // 更新自动玩UI状态
    private updateAutoPlayUI() {
        if (this.autoPlayCheckMark && GameUI.inst) {
            // 根据GameUI的自动玩状态更新勾选框（Sprite组件显示/隐藏）
            this.autoPlayCheckMark.active = GameUI.inst.isAutoPlaying;
        }
    }

    protected onHide(): void {
        Plat.hideBanner()
    }

    onClickClose() {
        AudioMgr.playSound(AudioPath.CLICK)
        GameUI.inst.is_game_pause=false;
        this.hideUI();
    }
    

    onClickHome()
    {
          this.hideUI();

          HomeUI.inst.showUI();
    }


    onClickResume()
    {
        GameUI.inst.is_game_pause=false;

        this.hideUI();
    }

    onClickPass()
    {
        AudioMgr.playSound(AudioPath.CLICK);
        Plat.showRewardVideo((success:boolean)=>{
            if(success){
                LocalData.lv++;
                GameUI.inst.onStartGame();
                this.hideUI();
            }
        })

    }

    // 勾选框点击处理
    onClickAutoPlayCheckbox() {
        console.log("=== 勾选框被点击 ===");
        AudioMgr.playSound(AudioPath.CLICK);

        if (!GameUI.inst) {
            console.log("GameUI.inst不存在");
            return;
        }

        console.log("点击前自动玩状态:", GameUI.inst.isAutoPlaying);

        // 切换自动玩状态
        if (GameUI.inst.isAutoPlaying) {
            GameUI.inst.stopAutoPlay();
        } else {
            GameUI.inst.startAutoPlay();
        }

        console.log("点击后自动玩状态:", GameUI.inst.isAutoPlaying);

        // 强制测试：直接设置CheckMark显示
        if (this.autoPlayCheckMark) {
            console.log("CheckMark节点存在，强制显示");
            this.autoPlayCheckMark.active = true;
            console.log("设置后CheckMark.active:", this.autoPlayCheckMark.active);
        } else {
            console.log("CheckMark节点不存在！");
        }

        // 更新UI显示（Sprite组件的显示/隐藏）
        this.updateAutoPlayUI();
    }

    // VIP状态变化事件处理
    private onVipStatusChanged(isVip: boolean) {
        console.log("PauseUI收到VIP状态变化:", isVip);
        this.updateVipUI();
    }

    // 更新VIP相关UI显示
    public updateVipUI() {
        const isVip = LocalData.isVipUser;
        console.log("更新VIP UI，当前VIP状态:", isVip);

        // 控制自动玩面板的显示
        if (this.autoPlayPanel) {
            this.autoPlayPanel.active = isVip;
            console.log("自动玩面板显示状态:", this.autoPlayPanel.active);
        }
    }

    // 销毁时清理事件监听
    onDestroy() {
        cc.systemEvent.off('vipStatusChanged', this.onVipStatusChanged, this);
    }

}
