// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { DataMgr } from "../manager/DataMgr";

const { ccclass, property } = cc._decorator;

@ccclass
export default class CatItem extends cc.Component {

    @property(cc.Node)
    cat: cc.Node = null;
    @property(cc.Sprite)
    catSprite: cc.Sprite = null


    start() {

        this.scheduleOnce(() => {
            this.play(1);
        }, 5)
    }

    protected onEnable(): void {
        const id = DataMgr.ins.getRandomId();
        cc.resources.load("/cat/cat_skin_" + id, cc.SpriteFrame, (error, frame) => {
            this.catSprite.spriteFrame = frame
        })
    }

    public state = 0;

    public play(state) {
        if (state == 1)  //默认
        {
            cc.tween(this.cat)
                .to(1, { scaleX: 1.2 })
                .to(1, { scaleX: 1 })
                .union()
                .repeat(1000000)
                .start();
        }
    }

}
