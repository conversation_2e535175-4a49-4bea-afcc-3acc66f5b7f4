<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{0,0},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{350,0},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{700,0},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>4.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{1050,0},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>5.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{0,600},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>6.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{350,600},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>7.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{700,600},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>hit.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{1050,600},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>pig.png</string>
            <key>size</key>
            <string>{1400,1200}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:fccec52a038564caf3b7c3ec9d13d512:3ab4b60d565b8d045c6cd239b6f14b21:cc7484018991b35fd066a21f6d8a9d46$</string>
            <key>textureFileName</key>
            <string>pig.png</string>
        </dict>
    </dict>
</plist>
