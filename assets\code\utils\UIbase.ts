

export default class UIbase extends cc.Component
{

   

    public showUI(data?:any)
    {

        if(this.node.parent!=null)
        {
            this.node.parent=null;
        }

        let s = cc.director.getScene();
        // let s = cc.director.getScene().getChildByName('Canvas');
        s.addChild(this.node);

        this.onShow()
    }

    public  hideUI()
    {
        this.onHide()
        this.node.removeFromParent(false);
    }

    protected onShow(){

    }

    protected onHide(){

    }
   
}
