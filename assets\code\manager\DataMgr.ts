class ILocalData {
    skins: number[];
    unlockSkinList: number[]
}

export class DataMgr {
    private static _ins: DataMgr;
    public static get ins(): DataMgr {
        this._ins = this._ins || new DataMgr()
        return this._ins;
    }

    data: ILocalData
    constructor() {
        this.load()
    }

    load() {
        const local_data_json = localStorage.getItem("local_data_json");
        if (!local_data_json) {
            this.data = {
                skins: [1],
                unlockSkinList: [1]
            }
        } else {
            this.data = JSON.parse(local_data_json)
        }
    }
    save() {
        const local_data_json = JSON.stringify(this.data)
        localStorage.setItem("local_data_json", local_data_json)
    }

    setSkin(skin: number) {
        let idx = this.data.skins.indexOf(skin);
        if (idx != -1)
        {
            this.data.skins.splice(idx, 1);
        }else{
            this.data.skins.push(skin);
        }
        if(this.data.skins.length <= 0){
            this.data.skins.push(1);
        }
        this.save()
    }

    isUnlockSkin(skin: number) {
        return this.data.unlockSkinList.includes(skin)
    }

    unlockSkin(skin: number) {
        if (this.data.unlockSkinList.includes(skin)) {
            return;
        }
        this.data.unlockSkinList.push(skin)
        this.save()
    }

    isUseSkin(skin: number):boolean{
        return this.data.skins.includes(skin);
    }

    getRandomId():number{
        let index = Math.floor(Math.random()*this.data.skins.length);
        return this.data.skins[index];
    }

}