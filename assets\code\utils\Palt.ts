
import Log from "./util/Log";
import GameUtil from "./util/GameUtil";

export default class Plat {

    public static screenWidth = 750;
    public static screenHeight = 1334;

    public static userinfo: any = null;


    public static plat_name = "cocos";

    public static pt: any = {};


    //记录一些微信数据
    public static code: string = "";
    public static sessionKey: string = "";

    public static isWX(): boolean {
        return this.plat_name == "wx";
    }


    public static isCocos() {
        return this.plat_name == "cocos";
    }

    public static getGameName() {
        return this.plat_name + "_hwdfk";  //好玩的方块
    }


    public static init() {

        // Log.warn("平台初始化123");

        let info: any = {};
        info.nickName = "临时玩家" + GameUtil.randomRange(1, 1000);
        info.avatarUrl = "res/1.png";

        this.userinfo = info;


        if (cc.sys.platform == cc.sys.WECHAT_GAME) {

            // Log.warn("微信平台初始化");


            this.plat_name = "wx";
            this.pt = wx;

            wx.showShareMenu({
                menus: ['shareAppMessage', "shareTimeline"],
                //  withShareTicket:true
            })

            wx.onShareAppMessage(function () {
                return {
                    title: '你能救救小猫吗?',
                    imageUrl: "https://mmocgame.qpic.cn/wechatgame/vynzf1zJd2HG5rxV0SEhDZbKJicqZIwoV0naJ6zu6c3Hq9P5c9Jk4ya32VGYt5YZW/0"
                }
            })


            if (wx.onShareTimeline != null) {
                wx.onShareTimeline(function () {
                    return {
                        title: '你能救救小猫吗?',
                        imageUrl: "https://mmocgame.qpic.cn/wechatgame/vynzf1zJd2HG5rxV0SEhDZbKJicqZIwoV0naJ6zu6c3Hq9P5c9Jk4ya32VGYt5YZW/0"
                        //   imageUrl: "https://mmocgame.qpic.cn/wechatgame/YyXzjOI0e4ibApMRqsoyJQvMaMsOiadNS8mdxDovz9xPj3odeRiafIyUptxRRycr4uu/0",
                        //   imagePreviewUrl:"https://mmocgame.qpic.cn/wechatgame/YyXzjOI0e4ibApMRqsoyJQvMaMsOiadNS8mdxDovz9xPj3odeRiafIyUptxRRycr4uu/0"
                    }
                })
            }

            this.initAds()
        }


        //注册显示回调
        if (this.pt.onShow != null) {
            this.pt.onShow(this.onshow);
        }


        //屏幕尺寸设置
        if (this.pt.getSystemInfoSync) {

            let system = this.pt.getSystemInfoSync();

            this.screenWidth = system.screenWidth;
            this.screenHeight = system.screenHeight;

            //  console.warn("屏幕宽:"+system.screenWidth);
            //  console.warn("屏幕高:"+system.screenHeight);
        }
    }



    private static fun_obj: any;
    private static share_obj: any;

    public static setShareCallback(obj: any, fun: any) {
        Plat.share_obj = obj;
        Plat.fun_obj = fun;
    }


    public static state = 0;
    //显示回调
    public static onshow(res) {
        // Log.warn("onshow-----")

        // Log.error(res)


        if (Plat.fun_obj != null) {
            Plat.fun_obj.call(Plat.share_obj);

            Plat.fun_obj = null;
            Plat.share_obj = null;
        }

        if (this.state == 0) {
            //   AdInterstitial.show();
        }

        this.state = 0;
    }

    //显示回调
    public static getLaunchOptionsSync(res) {
        // Log.warn("getLaunchOptionsSync-----")

        // Log.error(res)

    }

    public static showToast(str, jg = 2000) {
        if (this.pt.showToast != null) {
            this.pt.showToast({
                title: str,
                content: str,
                message: str,
                icon: 'none',
                duration: jg
            })
        }
    }

    public static hideToast() {
        if (this.pt.hideToast != null) {
            this.pt.hideToast({

            })
        }
    }

    public static shareAppMessage() {

        Plat.state = 1;

        if (this.pt.shareAppMessage == null) {
            if (Plat.fun_obj != null) {
                Plat.fun_obj.call(Plat.share_obj);

                Plat.fun_obj = null;
                Plat.share_obj = null;
            }

            return;
        };

        this.pt.shareAppMessage({
            title: '你能拯救小动物们吗?',
            imageUrl: "https://mmocgame.qpic.cn/wechatgame/vynzf1zJd2HG5rxV0SEhDZbKJicqZIwoV0naJ6zu6c3Hq9P5c9Jk4ya32VGYt5YZW/0",
            // imageUrl: "https://mmocgame.qpic.cn/wechatgame/YyXzjOI0e4ibApMRqsoyJQvMaMsOiadNS8mdxDovz9xPj3odeRiafIyUptxRRycr4uu/0",
            // imageUrlId:"N3jjVKcoSbGwfQ5LTudx0w==",
            // templateId:"6c06gh32j4iff897f9",
            desc: "-_-来玩玩吧"
        })
    }

    public static setRank(level) {
        let data = {
            event: 'score',
            level: (level) + ''
        }
        console.log('设置分数:', data.level)
        this.postMessage(data)
    }

    public static updateRank() {
        this.postMessage({
            event: 'update'
        })
    }

    public static postMessage(object: any) {
        object.type = 'engine';
        try {
            this.pt.getOpenDataContext().postMessage(object)
        } catch (error) {
            console.log(error)
        }
    }

    public static isBigSize() {
        if (Plat.screenHeight > Plat.screenWidth * 2) {
            return true;
        }

        return false;
    }

    private static rewardVideo2
    public static noAds = false;
    public static showRewardVideo(callback: Function) {

        let id = "adunit-244d020efbfc3268"

        if (this.rewardVideo2 != null) {
            this.rewardVideo2.offClose(fun);
        }

        if(typeof wx === 'undefined' ){
            console.log("Not in wechat")
            callback(true);
            return;
        }else if(this.noAds){
console.log("NotADS!!!")
            callback(true);
            return;
        }
        
        let rewardedVideoAd = wx.createRewardedVideoAd({
            adUnitId: id,
        });
        this.rewardVideo2 = rewardedVideoAd;
        rewardedVideoAd.load().then(() => {
           


            console.log('激励视频 广告加载成功');
            rewardedVideoAd.show();
        });
        rewardedVideoAd.onError(err => {
            console.log('激励视频 广告显示失败', err);
         
            callback(false);
        })
        var fun = function (res) {
            if (res && res.isEnded) {
                console.log('res:  ', res);
                callback(true);
                rewardedVideoAd.offClose(fun);
            } else {
                console.log('播放中途退出');
                callback(false);
            }
        }
        rewardedVideoAd.onClose(fun);
    }
    private static bannerAd = null
    private static interstitialAd = null
    public static initAds() {
        if (!wx) {
            return;
        }
        var size = cc.view.getFrameSize()
        this.bannerAd = wx.createBannerAd({
            adUnitId: '',
            style: {
                left: 0,
                top: size.height - 140,
                width: size.width
            }
        })
        this.bannerAd.onResize(res => {
            this.bannerAd.style.top = size.height - this.bannerAd.style.realHeight;
          });
        this.bannerAd.onError(err => {
            console.error(err.errMsg)
        });
        if (wx.createInterstitialAd) {
            this.interstitialAd = wx.createInterstitialAd({
                adUnitId: 'adunit-57ef9c5e5a322e0d'
            })
        }
    }

    public static showBanner() {
        if (this.bannerAd) {
            this.bannerAd.show()
        }
    }

    public static hideBanner() {
        if (this.bannerAd) {
            this.bannerAd.hide()
        }
    }

    public static showInterstitialAd() {
        if (this.interstitialAd) {
            this.interstitialAd.show().catch((err) => {
                console.error('插屏广告显示失败', err)
            })
        }
    }

static moban1 = null;
static moban2 = null;
static initMoban(){
     if (typeof wx == 'undefined' ) {
            return;
         }
      
 this.moban1 = wx.createCustomAd({
        adUnitId: 'adunit-c01764ef0590bf80',
        style: {
            left: 10,
            top:    200,
            width: 76, // 用于设置组件宽度，只有部分模板才支持，如矩阵格子模板
            fixed: true // fixed 只适用于小程序环境
        }
        })

this.moban2 = wx.createCustomAd({
  adUnitId: 'adunit-f060b74677e53976',
  style: {
    left: wx.getSystemInfoSync().windowWidth - 76,
    top:    200,
    width: 76, // 用于设置组件宽度，只有部分模板才支持，如矩阵格子模板
    fixed: true // fixed 只适用于小程序环境
  }
})


}

    public static showMoban(){
         if (typeof wx == 'undefined' ) {
            return;
         }
      
       
    if( this.moban1. isShow())return
    this.moban1.show()
    this.showMoban1()
 }


    

    public static showMoban1(){ if (typeof wx == 'undefined') {
            return;
        }


this.moban2.show()
    }

    static hidemoban(){
        
        if(!this.moban1 || !this.moban1. isShow())return
        this.moban1.hide();
        this.moban2.hide();
    }
}