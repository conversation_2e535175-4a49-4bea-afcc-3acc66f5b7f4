const style = require('./render/style')
const template = require('./render/template')
const Layout = require('./engine').default;

let __env = GameGlobal.wx || GameGlobal.tt || GameGlobal.swan;
let sharedCanvas = __env.getSharedCanvas();
let sharedContext = sharedCanvas.getContext('2d');

function draw(data) {
  Layout.clear();
  Layout.init(template(data), style);
  Layout.layout(sharedContext);
}

function updateViewPort() {
  Layout.updateViewPort({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });
}

__env.onMessage(data => {
  if (data.type === 'engine') {
    switch (data.event) {
      case 'update':
        //刷新排行榜
        console.log(1111);
        wx.getFriendCloudStorage({
          keyList: ['score'],
          success: (datas) => {
            console.log(datas)
            let rank = [];
            datas.data.forEach(item => {
              rank.push({
                rankScore: Number(item.KVDataList[0].value),
                nickname: item.nickname,
                avatarUrl: item.avatarUrl,
              })
            })
            rank.sort((a, b) => b.rankScore - a.rankScore);
            rank.length = Math.min(rank.length, 5)
            updateViewPort();
            draw({
              data: rank
            });
          }
        })
        break;
      case 'score':
        //更新发分数
        wx.getUserCloudStorage({
          keyList: ['score'],
          success: (kvData) => {
            if (kvData.KVDataList && kvData.KVDataList.length > 0) {
              let level = Number(kvData.KVDataList[0].value)
              if (level < data.level) {
                wx.setUserCloudStorage({
                  KVDataList: [{
                    key: 'score',
                    value: String(data.level)
                  }],
                  success: () => {
                    console.log('设置分数完成' + data.level)
                  }
                })
              } else {
                console.log('设置的分数比最高分数低', level, data.level)
              }
            }else{
              wx.setUserCloudStorage({
                KVDataList: [{
                  key: 'score',
                  value: String(data.level)
                }],
                success: () => {
                  console.log('设置分数完成' + data.level)
                }
              })
            }

          }
        })

        break;

      default:
        break;
    }

  }
});