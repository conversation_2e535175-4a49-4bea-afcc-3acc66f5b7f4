// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import AudioPath from '../datas/AudioPath';
import AudioMgr from '../manager/AudioMgr';
import LocalData from '../manager/LocalData';
import PrefabUtil from '../utils/manager/PrefabUtil';
import Plat from '../utils/Palt';
import UIbase from '../utils/UIbase';
import GameUI from './GameUI';
import TipsUI from './TipsUI';

const {ccclass, property} = cc._decorator;

@ccclass
export default class ChangeGameBgUI extends UIbase {

    @property(cc.Node)
    btn_conatiner:cc.Node = null;

    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    start () {

    }

    // update (dt) {}

    private static _inst:ChangeGameBgUI;

    public static get inst()
    {
        if(this._inst==null  || this._inst.node==null)
        {
            let prefab = PrefabUtil.get("ChangeGameBgUI");
            if (!prefab) {
                console.error("ChangeGameBgUI预制体未找到");
                return null;
            }
            let v=cc.instantiate(prefab);

            this._inst=v.getComponent(ChangeGameBgUI);
        }

        return this._inst;
    }

    protected onShow(): void {
        // Plat.showBanner()
        this.updateBg();
    }

    //利用btn container的名字来分辨bg1 - bg6
    updateBg() {
        if(this.btn_conatiner)
        {
            this.btn_conatiner.children.forEach((node)=>{
                if(node.name != LocalData.GameBgId)
                {
                    node.children.forEach((n)=>{
                        n.active = false;
                    })
                }else if(node.name == LocalData.GameBgId)

                    {
                        node.children.forEach((n)=>{
                            n.active = true;
                        })
                    }
            })
        }
    }

    protected onHide(): void {
        Plat.hideBanner()
    }

    onClickClose() {
        AudioMgr.playSound(AudioPath.CLICK)
        GameUI.inst.is_game_pause = false;
        this.hideUI();
    }

    onClickChangeBgBtn(event:cc.Event.EventTouch, bgID)
    {
        if(LocalData.GameBgId == bgID){
            TipsUI.inst.showTips("已经是当前的背景")
        }else{
            LocalData.GameBgId = bgID;
            this.updateBg();
            this.updateGameBg();
            TipsUI.inst.showTips("更换背景成功")
        }
    }

    updateGameBg() {
        GameUI.inst.updateBg();
    }

    
}
