import LocalData from "../manager/LocalData";
import { skin } from "./Constants";



export default class mySkinData {

    //用户已拥有的排在前面，后面的按照order从小到大排序，默认起始拥有一个皮肤在localdata 方法里修改，默认rabbit
    public static skins: Array<skin> = [
        { id: "rabbit", name: "兔子", path: "rabbit", order: 1 , isCurrent:false},
        { id: "panda", name: "熊猫", path: "panda", order: 2 , isCurrent:false},
        { id: "raccoon", name: "浣熊", path: "raccoon", order: 3 , isCurrent:false},
        { id: "pig", name: "猪", path: "pig", order: 4 , isCurrent:false},
        { id: "cat", name: "猫", path: "cat", order: 5 , isCurrent:false},
        { id: "alpaca", name: "羊驼", path: "alpaca", order: 6 , isCurrent:false},
        { id: "tiger", name: "老虎", path: "tiger", order: 7 , isCurrent:false},
        { id: "kangaroo", name: "袋鼠", path: "kangaroo", order: 8 , isCurrent:false},
        { id: "horse", name: "马", path: "horse", order: 9 , isCurrent:false},
        { id: "parrot", name: "鹦鹉", path: "parrot", order: 10, isCurrent:false }
    ]

    //只计算当前皮肤里有的
    public static getCollectionProgress():number{
        return LocalData.obtainedSkins.filter(item => this.skins.some(obj => obj.id === item)).length;
    }

    public static getSkins():Array<skin> {
        // 排序和标注皮肤
        const sortedSkins = this.skins.sort((a, b) => {
            const aObtained = LocalData.obtainedSkins.includes(a.name);
            const bObtained = LocalData.obtainedSkins.includes(b.name);

            if (aObtained && !bObtained) {
                return -1;
            }
            if (!aObtained && bObtained) {
                return 1;
            }
            if (a.name === LocalData.currentSkin) {
                return -1;
            }
            if (b.name === LocalData.currentSkin) {
                return 1;
            }
            return a.order - b.order;
        });

        // 标注当前使用的皮肤
        const finalSkins = sortedSkins.map(skin => ({
            ...skin,
            isCurrent: skin.id === LocalData.currentSkin
        }));

        return finalSkins;
    }

    public static findMinOrderElementNotInSkins(): skin | null {
        
        // 过滤掉a数组中在b数组中的元素
        const filtered = this.skins.filter(item => !LocalData.obtainedSkins.includes(item.id));
    
        if (filtered.length === 0) {
            return null;
        }
    
        // 找到order最小的元素
        let minOrderElement:skin = filtered[0];
        for (const element of filtered) {
            if (element.order < minOrderElement.order) {
                minOrderElement = element;
            }
        }
 
        return minOrderElement;
    }

}