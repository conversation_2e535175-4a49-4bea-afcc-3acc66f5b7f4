// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { DataMgr } from "../manager/DataMgr";
import LocalData from "../manager/LocalData";
import Plat from "../utils/Palt";
import UIbase from "../utils/UIbase";
import PrefabUtil from "../utils/manager/PrefabUtil";

const { ccclass, property } = cc._decorator;

@ccclass
export default class rankUI extends UIbase {

    private static _inst: rankUI;

    public static get inst() {
        if (this._inst == null) {
            let prefab = PrefabUtil.get("rankUI");
            if (!prefab) {
                console.error("rankUI预制体未找到");
                return null;
            }
            let v = cc.instantiate(prefab);

            this._inst = v.getComponent(rankUI);
        }

        return this._inst;
    }

    @property(cc.SubContextView)
    view: cc.SubContextView = null;

    public showUI(data?: any): void {
        super.showUI(data)
        Plat.setRank(LocalData.lv)
        Plat.updateRank()
    }

    // update (dt) {}
}
