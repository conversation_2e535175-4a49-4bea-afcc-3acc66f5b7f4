// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import LocalData from "../manager/LocalData";
import Plat from "../utils/Palt";
import UIbase from "../utils/UIbase";
import PrefabUtil from "../utils/manager/PrefabUtil";
import provincesData from "../datas/provincesData";

const { ccclass, property } = cc._decorator;

@ccclass
export default class regionalUI extends UIbase {

    private static _inst: regionalUI;

    @property(cc.Node)
    scrollViewContent: cc.Node = null;

    @property(cc.Prefab)
    regionalItemPrefab:cc.Prefab = null; 

    @property(cc.Prefab)
    regionalUserItemPrefab:cc.Prefab = null; 

    public static get inst() {
        if (this._inst == null) {
            let prefab = PrefabUtil.get("regionalUI");
            if (!prefab) {
                console.error("regionalUI预制体未找到");
                return null;
            }
            let v = cc.instantiate(prefab);

            this._inst = v.getComponent(regionalUI);
        }

        return this._inst;
    }



    public showUI(data?: any): void {
        super.showUI(data)
        this.spawnItems();
    }

    	
    private spawnItems () {
        const provincesArray = provincesData.getProvinceRank()

        for (let i = 0; i < provincesArray.length; i++) {
            let item = cc.instantiate(this.regionalItemPrefab);
            if(LocalData.userRegional == provincesArray[i].id){
                item = cc.instantiate(this.regionalUserItemPrefab);
            }

            let itemScript = item.getComponent("provinceRankRow")
            itemScript.updateUi(provincesArray[i].name, `${i+1}`, provincesArray[i].score)
            
            this.scrollViewContent.addChild(item);
        }
    }

    // update (dt) {}
}
