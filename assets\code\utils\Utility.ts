// const { _decorator, Component, Node, Sprite, Texture2D, Button, Vec3 } = cc;
const {ccclass, property} = cc._decorator;

@ccclass
export default class Utility 
{
    public static changeBackground(spriteFrame:cc.SpriteFrame, Sprite:cc.Sprite)
    {
        // 更换背景图片的纹理
        // const texture = new Texture2D();
        // texture.image = new Image();
        // texture.image.src = 'new_background.png'; // 替换为你的背景图片路径
        // this.backgroundSprite.spriteFrame = new SpriteFrame();
        // this.backgroundSprite.spriteFrame.texture = texture;
        Sprite.spriteFrame = spriteFrame;
    }
}