const { ccclass, property } = cc._decorator;
import AudioPath from '../datas/AudioPath';
import AudioMgr from '../manager/AudioMgr';
import { DataMgr } from '../manager/DataMgr';
import PrefabUtil from '../utils/manager/PrefabUtil';
import Plat from '../utils/Palt';
import UIbase from '../utils/UIbase';
import GameUI from './GameUI';


@ccclass
export default class FailUI extends UIbase {

    private static _inst: FailUI;

    public static get inst() {
        if (this._inst == null) {
            let prefab = PrefabUtil.get("FailUI");
            if (!prefab) {
                console.error("FailUI预制体未找到");
                return null;
            }
            let v = cc.instantiate(prefab);

            this._inst = v.getComponent(FailUI);
        }

        return this._inst;
    }

    @property(cc.Sprite)
    cat: cc.Sprite = null;

    private isAutoPlaying: boolean = false; // 自动玩状态
    private autoClickTimer: number = null; // 自动点击定时器

    public showUI(data?: any): void {
        super.showUI(data)

        // 接收自动玩状态参数
        this.isAutoPlaying = data || false;
        console.log("FailUI显示，自动玩状态:", this.isAutoPlaying);

        // 如果是自动玩模式，3秒后自动重试
        if (this.isAutoPlaying) {
            console.log("自动玩模式：3秒后自动重试当前关卡");
            this.autoClickTimer = setTimeout(() => {
                console.log("自动玩：模拟点击重新开始按钮");
                this.onClickStartGame();
            }, 3000);
        }

        const id = DataMgr.ins.getRandomId();
        cc.resources.load("/cat/cat_skin_" + id, cc.SpriteFrame, (error, frame) => {
            this.cat.spriteFrame = frame
        })
    }

    protected onShow(): void {
        // Plat.showBanner()
        // Plat.showInterstitialAd()
    }

    protected onHide(): void {
        // Plat.hideBanner()

        // 清理自动点击定时器
        if (this.autoClickTimer) {
            clearTimeout(this.autoClickTimer);
            this.autoClickTimer = null;
        }
    }

    public onClickStartGame() {
        console.log("FailUI点击重新开始，自动玩状态:", this.isAutoPlaying);
        this.hideUI();
        GameUI.inst.onStartGame();

        // 如果之前是自动玩模式，在重新开始后恢复自动玩
        if (this.isAutoPlaying) {
            console.log("自动玩模式：重新开始后恢复自动玩");
            // 延迟一点时间确保游戏完全初始化后再开始自动玩
            setTimeout(() => {
                if (GameUI.inst && GameUI.inst.is_game_playing) {
                    GameUI.inst.startAutoPlay();
                    console.log("自动玩已恢复");
                }
            }, 1000);
        }

        AudioMgr.playSound(AudioPath.CLICK);
    }

    public onClickShare() {
        Plat.shareAppMessage();
        AudioMgr.playSound(AudioPath.CLICK);
    }



}