const {ccclass, property} = cc._decorator;
 
@ccclass
export default class provinceRankRow extends cc.Component {
    
    @property(cc.Label)
    provincePosition: cc.Label = null;

    @property(cc.Label)
    provinceName: cc.Label = null;

    @property(cc.Label)
    provinceScore: cc.Label = null;
 
    
    public updateUi(name:string, position:string, score : number)
    {
        this.provinceName.string = name;
        this.provinceScore.string = score.toString();
        this.provincePosition.string = position;
    }

}