/**
 * VIP系统测试辅助工具
 * 用于开发和调试阶段测试VIP功能
 */
import LocalData from '../manager/LocalData';
import InvitationCodes from '../datas/InvitationCodes';

export default class VipTestHelper {
    
    /**
     * 重置VIP状态（用于测试）
     */
    public static resetVipStatus() {
        LocalData.isVipUser = false;
        LocalData.usedRedeemCode = "";
        console.log("VIP状态已重置");
    }
    
    /**
     * 设置VIP状态（用于测试）
     */
    public static setVipStatus(isVip: boolean) {
        LocalData.isVipUser = isVip;
        console.log("VIP状态设置为:", isVip);
        
        // 发送状态变化事件
        cc.systemEvent.emit('vipStatusChanged', isVip);
    }
    
    /**
     * 获取当前VIP状态
     */
    public static getVipStatus(): boolean {
        const status = LocalData.isVipUser;
        console.log("当前VIP状态:", status);
        return status;
    }
    
    /**
     * 获取随机测试邀请码
     */
    public static getTestCode(): string {
        const code = InvitationCodes.getRandomCode();
        console.log("测试邀请码:", code);
        return code;
    }
    
    /**
     * 验证邀请码（用于测试）
     */
    public static testValidateCode(code: string): boolean {
        const isValid = InvitationCodes.validateCode(code);
        console.log(`邀请码 ${code} 验证结果:`, isValid);
        return isValid;
    }
    
    /**
     * 获取已使用的邀请码
     */
    public static getUsedCode(): string {
        const code = LocalData.usedRedeemCode;
        console.log("已使用的邀请码:", code);
        return code;
    }
    
    /**
     * 打印所有VIP相关信息
     */
    public static printVipInfo() {
        console.log("=== VIP系统状态信息 ===");
        console.log("VIP状态:", LocalData.isVipUser);
        console.log("已使用邀请码:", LocalData.usedRedeemCode);
        console.log("邀请码总数:", InvitationCodes.getCodeCount());
        console.log("=====================");
    }
    
    /**
     * 测试常用邀请码
     */
    public static getCommonTestCodes(): string[] {
        return [
            "A7K9M2", "B8L3N4", "C9M4O5", "D1N5P6", "E2O6Q7",
            "F3P7R8", "G4Q8S9", "H5R9T1", "I6S1U2", "J7T2V3"
        ];
    }
}

// 在开发环境下将测试工具挂载到全局对象
if (CC_DEV) {
    (window as any).VipTestHelper = VipTestHelper;
    console.log("VIP测试工具已加载，可在控制台使用 VipTestHelper");
}
