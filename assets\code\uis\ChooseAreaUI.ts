// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import LocalData from "../manager/LocalData";
import Plat from "../utils/Palt";
import UIbase from "../utils/UIbase";
import PrefabUtil from "../utils/manager/PrefabUtil";
import provincesData from "../datas/provincesData";
import provinceBtn from "../scripts/item/provinceBtn";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ChooseAreaUI extends UIbase {

    private static _inst: ChooseAreaUI;

    @property(cc.Node)
    scrollViewContent: cc.Node = null;

    @property(cc.Prefab)
    provinceBtnPrefab: cc.Prefab = null;

    @property(cc.Label)
    currentLabel: cc.Label = null;

    //省份按钮脚本
    private btnScript: provinceBtn = null;

    public static get inst() {
        if (this._inst == null) {
            let prefab = PrefabUtil.get("ChooseAreaUI");
            if (!prefab) {
                console.error("ChooseAreaUI预制体未找到");
                return null;
            }
            let v = cc.instantiate(prefab);

            this._inst = v.getComponent(ChooseAreaUI);
        }

        return this._inst;
    }

    protected onLoad(): void {
        this.changeLabel();
        this.spawnItems();

        // this.node.on('provinceSelect', (event: cc.Event.EventCustom)=>{
        //     let script = event.getUserData();
            
        //     if(this.btnScript == null){
        //         this.btnScript = script;
        //         this.btnScript.updateBg();
        //     }else if(this.btnScript !== script)
        //     {
        //         this.btnScript.updateBg();
        //         script.updateBg();
        //         this.btnScript =  script;
        //     }
        //     this.changeLabel();
        //     event.stopPropagation();
        // });

        cc.systemEvent.on('provinceSelect', (script:provinceBtn)=>{            
            if(this.btnScript == null){
                this.btnScript = script;
                this.btnScript.updateBg();
            }else if(this.btnScript !== script)
            {
                this.btnScript.updateBg();
                script.updateBg();
                this.btnScript =  script;
            }
            this.changeLabel();
        });
    }


    public showUI(data?: any): void {
        super.showUI(data)
    }

    private changeLabel() {

        if (LocalData.userRegional !== '') {
            this.currentLabel.string = '当前所在地：' + ' ' + provincesData.provinces[LocalData.userRegional].name
        }

    }

    private spawnItems() {
        const provincesArray = provincesData.getProvinceRank()

        for (let i = 0; i < provincesArray.length; i++) {
            let btn = cc.instantiate(this.provinceBtnPrefab);

            let btnScript:provinceBtn = btn.getComponent("provinceBtn")
            btnScript.updateUi(provincesArray[i].name, provincesArray[i].id)

            if (LocalData.userRegional == provincesArray[i].id) {
                btnScript.updateBg();
                this.btnScript = btnScript;
            }

            this.scrollViewContent.addChild(btn);
        }
    }

    // update (dt) {}
}
