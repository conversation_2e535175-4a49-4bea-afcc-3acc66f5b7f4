<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>anchor</key>
                <string>{0.5,0.5}</string>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{305,540}</string>
                <key>spriteSourceSize</key>
                <string>{305,540}</string>
                <key>textureRect</key>
                <string>{{0,0},{305,540}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>anchor</key>
                <string>{0.5,0.5}</string>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{305,540}</string>
                <key>spriteSourceSize</key>
                <string>{305,540}</string>
                <key>textureRect</key>
                <string>{{305,0},{305,540}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>anchor</key>
                <string>{0.5,0.5}</string>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{305,540}</string>
                <key>spriteSourceSize</key>
                <string>{305,540}</string>
                <key>textureRect</key>
                <string>{{610,0},{305,540}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>4.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>anchor</key>
                <string>{0.5,0.5}</string>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{305,540}</string>
                <key>spriteSourceSize</key>
                <string>{305,540}</string>
                <key>textureRect</key>
                <string>{{915,0},{305,540}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>5.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>anchor</key>
                <string>{0.5,0.5}</string>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{305,540}</string>
                <key>spriteSourceSize</key>
                <string>{305,540}</string>
                <key>textureRect</key>
                <string>{{1220,0},{305,540}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>6.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>anchor</key>
                <string>{0.5,0.5}</string>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{305,540}</string>
                <key>spriteSourceSize</key>
                <string>{305,540}</string>
                <key>textureRect</key>
                <string>{{0,540},{305,540}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>7.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>anchor</key>
                <string>{0.5,0.5}</string>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{305,540}</string>
                <key>spriteSourceSize</key>
                <string>{305,540}</string>
                <key>textureRect</key>
                <string>{{305,540},{305,540}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>8.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>anchor</key>
                <string>{0.5,0.5}</string>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{305,540}</string>
                <key>spriteSourceSize</key>
                <string>{305,540}</string>
                <key>textureRect</key>
                <string>{{610,540},{305,540}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>9.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>anchor</key>
                <string>{0.5,0.5}</string>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{305,540}</string>
                <key>spriteSourceSize</key>
                <string>{305,540}</string>
                <key>textureRect</key>
                <string>{{915,540},{305,540}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>hit.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>anchor</key>
                <string>{0.5,0.5}</string>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{305,540}</string>
                <key>spriteSourceSize</key>
                <string>{305,540}</string>
                <key>textureRect</key>
                <string>{{1220,540},{305,540}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>panda_color.png</string>
            <key>size</key>
            <string>{1525,1080}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:panda_custom_frames$</string>
            <key>textureFileName</key>
            <string>panda_color.png</string>
        </dict>
    </dict>
</plist>