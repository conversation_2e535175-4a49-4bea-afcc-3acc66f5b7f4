import AnimalItem from "./AnimalItem";

const { ccclass, property } = cc._decorator;

/**
 * 简化版换色动画组件
 * 专门用于处理单个换色过渡图集
 */
@ccclass
export default class SimpleColorTransition extends cc.Component {

    @property({
        displayName: "换色过渡图集",
        tooltip: "包含换色过渡动画的图集"
    })
    transitionAtlas: cc.SpriteAtlas = null;

    @property({
        displayName: "动画帧数",
        tooltip: "换色动画的总帧数"
    })
    frameCount: number = 7;

    @property({
        displayName: "动画帧率",
        tooltip: "动画播放的帧率(FPS)"
    })
    frameRate: number = 6;

    private isPlaying: boolean = false;

    /**
     * 播放换色过渡动画
     * @param animalItem 要换色的动物组件
     * @param targetAtlas 目标图集（换色后的图集）
     * @param onComplete 动画完成回调
     */
    public playColorTransition(animalItem: AnimalItem, targetAtlas: cc.SpriteAtlas, onComplete?: () => void) {
        if (this.isPlaying || !animalItem || !animalItem.isValid || !this.transitionAtlas) {
            // 如果没有过渡动画，直接切换
            this.completeTransition(animalItem, targetAtlas, onComplete);
            return;
        }

        this.isPlaying = true;
        this.playTransitionAnimation(animalItem, () => {
            this.completeTransition(animalItem, targetAtlas, onComplete);
        });
    }

    /**
     * 播放过渡动画
     */
    private playTransitionAnimation(animalItem: AnimalItem, onComplete: () => void) {
        const animalNode = animalItem.animalNode;
        if (!animalNode) {
            onComplete();
            return;
        }

        let animation = animalNode.getComponent(cc.Animation);
        if (!animation) {
            onComplete();
            return;
        }

        // 停止当前动画
        animation.stop();

        // 创建过渡动画帧
        const transitionFrames = this.createTransitionSpriteFrames();
        
        if (transitionFrames.length === 0) {
            cc.warn("换色过渡帧创建失败");
            onComplete();
            return;
        }

        // 清除现有的过渡动画剪辑
        const clips = animation.getClips();
        for (let clip of clips) {
            if (clip.name === "anim_color_transition") {
                animation.removeClip(clip);
                break;
            }
        }

        // 创建过渡动画剪辑
        const transitionClip = cc.AnimationClip.createWithSpriteFrames(transitionFrames, this.frameRate);
        transitionClip.name = "anim_color_transition";
        transitionClip.wrapMode = cc.WrapMode.Normal; // 只播放一次
        transitionClip.speed = 1;

        animation.addClip(transitionClip);

        // 监听动画完成事件
        const onAnimationComplete = (_type: string, state: cc.AnimationState) => {
            if (state.name === "anim_color_transition") {
                animation.off('stop', onAnimationComplete, this);
                onComplete();
            }
        };

        animation.on('stop', onAnimationComplete, this);
        animation.play('anim_color_transition');
    }

    /**
     * 创建过渡动画的精灵帧
     */
    private createTransitionSpriteFrames(): cc.SpriteFrame[] {
        const frames: cc.SpriteFrame[] = [];
        
        for (let i = 1; i <= this.frameCount; i++) {
            // 尝试多种帧名称格式
            const frameNames = [
                i.toString(),           // "1", "2", "3"...
                `${i}.PNG`,            // "1.PNG", "2.PNG", "3.PNG"...
                `${i}.png`,            // "1.png", "2.png", "3.png"...
                `frame_${i}`,          // "frame_1", "frame_2"...
                `${i.toString().padStart(2, '0')}` // "01", "02", "03"...
            ];

            let spriteFrame: cc.SpriteFrame = null;
            for (const frameName of frameNames) {
                spriteFrame = this.transitionAtlas.getSpriteFrame(frameName);
                if (spriteFrame) {
                    break;
                }
            }

            if (spriteFrame) {
                frames.push(spriteFrame);
            } else {
                cc.warn(`换色过渡帧 ${i} 未找到`);
            }
        }
        
        return frames;
    }

    /**
     * 完成换色过渡
     */
    private completeTransition(animalItem: AnimalItem, targetAtlas: cc.SpriteAtlas, onComplete?: () => void) {
        this.isPlaying = false;
        
        // 切换到目标图集
        if (targetAtlas) {
            animalItem.setAtlas(targetAtlas);
        }
        
        // 调用完成回调
        if (onComplete) {
            onComplete();
        }
    }

    /**
     * 检查是否正在播放换色动画
     */
    public isTransitionPlaying(): boolean {
        return this.isPlaying;
    }

    /**
     * 设置换色过渡图集
     */
    public setTransitionAtlas(atlas: cc.SpriteAtlas) {
        this.transitionAtlas = atlas;
    }

    /**
     * 预览换色动画帧（调试用）
     */
    public previewFrames() {
        if (!this.transitionAtlas) {
            cc.warn("换色过渡图集未设置");
            return;
        }

        const frames = this.createTransitionSpriteFrames();
        cc.log(`换色过渡动画共有 ${frames.length} 帧`);
        
        frames.forEach((frame, index) => {
            if (frame) {
                cc.log(`第 ${index + 1} 帧: ${frame.name}`);
            }
        });
    }
}
