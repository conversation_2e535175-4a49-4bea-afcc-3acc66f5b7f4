// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import UIbase from "../utils/UIbase";
import PrefabUtil from "../utils/manager/PrefabUtil";
import ShopSkinItem from "./ShopSkinItem";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ShopUI extends UIbase {

    private static _inst: ShopUI;

    public static get inst() {
        if (this._inst == null) {
            let prefab = PrefabUtil.get("ShopUI");
            if (!prefab) {
                console.error("ShopUI预制体未找到");
                return null;
            }
            let v = cc.instantiate(prefab);

            this._inst = v.getComponent(ShopUI);
        }

        return this._inst;
    }

    @property([cc.Node])
    nodes: cc.Node[] = [];

    protected start(): void {

    }

    protected onEnable(): void {
        this.updateNodesData()
    }

    updateNodesData() {
        this.nodes.forEach(item => {
            item.getComponent(ShopSkinItem).setData()
        })
    }
    // update (dt) {}
}
