const { ccclass, property } = cc._decorator;
import UIbase from '../utils/UIbase';
import PrefabUtil from '../utils/manager/PrefabUtil';
import AudioMgr from '../manager/AudioMgr';
import AudioPath from '../datas/AudioPath';
import LocalData from '../manager/LocalData';
import InvitationCodes from '../datas/InvitationCodes';

@ccclass
export default class InvitationUI extends UIbase {

    @property(cc.EditBox)
    input: cc.EditBox = null; // 邀请码输入框

    @property(cc.Button)
    checkmark: cc.Button = null; // 确认按钮

    @property(cc.Button)
    btn_close: cc.Button = null; // 关闭按钮

    @property(cc.Label)
    messageLabel: cc.Label = null; // 消息显示标签

    @property(cc.Node)
    panel: cc.Node = null; // 弹窗主体

    private static _inst: InvitationUI;

    public static get inst() {
        console.log("=== InvitationUI.inst 被调用 ===");

        if (this._inst == null || this._inst.node == null) {
            console.log("需要创建新的InvitationUI实例");

            let prefab = PrefabUtil.get("InvitationUI");
            console.log("获取到的预制体:", prefab);

            if (!prefab) {
                console.error("❌ InvitationUI预制体未找到");
                return null;
            }

            console.log("开始实例化预制体");
            let v = cc.instantiate(prefab);
            console.log("预制体实例化完成，节点:", v);

            console.log("获取InvitationUI组件");
            this._inst = v.getComponent(InvitationUI);
            console.log("组件获取结果:", this._inst);

            if (!this._inst) {
                console.error("❌ 无法获取InvitationUI组件，请检查预制体是否正确添加了脚本组件");
                return null;
            }
        }

        console.log("返回InvitationUI实例:", this._inst);
        return this._inst;
    }

    start() {
        this.initUI();
    }

    private initUI() {
        // 初始化输入框
        if (this.input) {
            this.input.string = "";
            this.input.placeholder = "请输入6位邀请码";
            this.input.maxLength = 6;
            
            // 监听输入框回车事件
            this.input.node.on('editing-return', this.onInputReturn, this);
        }

        // 初始化消息标签
        if (this.messageLabel) {
            this.messageLabel.string = "";
            this.messageLabel.node.active = false;
        }

        // 绑定按钮事件
        if (this.checkmark) {
            this.checkmark.node.on(cc.Node.EventType.TOUCH_END, this.onClickCheckmark, this);
        }

        if (this.btn_close) {
            this.btn_close.node.on(cc.Node.EventType.TOUCH_END, this.onClickClose, this);
        }
    }

    protected onShow(): void {
        console.log("=== InvitationUI.onShow 被调用 ===");

        // 显示时清空输入框和消息
        if (this.input) {
            this.input.string = "";
            console.log("✅ 输入框已清空");
        } else {
            console.warn("⚠️ input输入框未绑定");
        }

        if (this.messageLabel) {
            this.messageLabel.string = "";
            this.messageLabel.node.active = false;
            console.log("✅ 消息标签已重置");
        } else {
            console.warn("⚠️ messageLabel未绑定");
        }

        // 播放弹窗动画
        console.log("开始播放弹窗动画");
        this.playShowAnimation();

        console.log("=== InvitationUI显示完成 ===");
    }

    protected onHide(): void {
        // 隐藏时清理
        this.hideMessage();
    }

    /**
     * 输入框回车事件处理
     */
    private onInputReturn() {
        this.validateInvitationCode();
    }

    /**
     * 确认按钮点击事件
     */
    public onClickCheckmark() {
        AudioMgr.playSound(AudioPath.CLICK);
        this.validateInvitationCode();
    }

    /**
     * 关闭按钮点击事件
     */
    public onClickClose() {
        AudioMgr.playSound(AudioPath.CLICK);
        this.hideUI();
    }

    /**
     * 验证邀请码
     */
    private validateInvitationCode() {
        if (!this.input) {
            console.error("输入框未绑定");
            return;
        }

        const code = this.input.string.trim();

        // 检查输入长度
        if (code.length !== 6) {
            this.showMessage("请输入6位邀请码", false);
            return;
        }

        // 移除VIP状态检查，允许无限次使用
        // 注释掉原来的VIP检查逻辑
        // if (LocalData.isVipUser) {
        //     this.showMessage("您已经是VIP用户", true);
        //     return;
        // }

        // 验证邀请码
        if (InvitationCodes.validateCode(code)) {
            this.onInvitationSuccess(code);
        } else {
            this.showMessage("邀请码错误", false);
        }
    }

    /**
     * 邀请码验证成功处理
     */
    private onInvitationSuccess(code: string) {
        console.log("邀请码验证成功（无限次使用）:", code);

        // 设置VIP状态（每次都可以设置）
        LocalData.isVipUser = true;

        // 记录使用的邀请码（但不限制重复使用）
        LocalData.usedRedeemCode = code;

        // 显示成功消息
        const isAlreadyVip = LocalData.isVipUser;
        if (isAlreadyVip) {
            this.showMessage("兑换码使用成功！VIP权限已刷新", true);
        } else {
            this.showMessage("登陆成功！您已获得VIP权限", true);
        }

        // 发送VIP状态变化事件
        cc.systemEvent.emit('vipStatusChanged', true);

        console.log("✅ 兑换码可无限次使用 - 设备无关 - 无任何限制");

        // 2秒后自动关闭界面
        this.scheduleOnce(() => {
            this.hideUI();
        }, 2);
    }

    /**
     * 显示消息
     */
    private showMessage(message: string, isSuccess: boolean) {
        if (!this.messageLabel) {
            console.error("消息标签未绑定");
            return;
        }

        this.messageLabel.string = message;
        this.messageLabel.node.active = true;
        
        // 设置消息颜色
        if (isSuccess) {
            this.messageLabel.node.color = cc.Color.GREEN;
        } else {
            this.messageLabel.node.color = cc.Color.RED;
        }

        // 播放消息动画
        this.playMessageAnimation();
    }

    /**
     * 隐藏消息
     */
    private hideMessage() {
        if (this.messageLabel) {
            this.messageLabel.node.active = false;
        }
    }

    /**
     * 播放显示动画
     */
    private playShowAnimation() {
        console.log("=== 播放显示动画 ===");

        if (!this.panel) {
            console.warn("⚠️ panel节点未绑定，无法播放动画");
            return;
        }

        console.log("panel节点存在，开始动画");
        console.log("panel初始状态 - 位置:", this.panel.position, "缩放:", this.panel.scale);

        // 初始状态：缩放为0
        this.panel.scale = 0;

        // 缩放动画
        cc.tween(this.panel)
            .to(0.3, { scale: 1 }, { easing: 'backOut' })
            .call(() => {
                console.log("✅ 弹窗动画播放完成");
            })
            .start();
    }

    /**
     * 播放消息动画
     */
    private playMessageAnimation() {
        if (!this.messageLabel) return;

        // 淡入动画
        this.messageLabel.node.opacity = 0;
        cc.tween(this.messageLabel.node)
            .to(0.2, { opacity: 255 })
            .start();
    }

    /**
     * 销毁时清理事件监听
     */
    onDestroy() {
        if (this.input) {
            this.input.node.off('editing-return', this.onInputReturn, this);
        }
        if (this.checkmark) {
            this.checkmark.node.off(cc.Node.EventType.TOUCH_END, this.onClickCheckmark, this);
        }
        if (this.btn_close) {
            this.btn_close.node.off(cc.Node.EventType.TOUCH_END, this.onClickClose, this);
        }
    }
}
