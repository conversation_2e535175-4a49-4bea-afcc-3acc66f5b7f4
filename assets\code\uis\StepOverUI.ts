// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import Plat from "../utils/Palt";
import UIbase from "../utils/UIbase";
import PrefabUtil from "../utils/manager/PrefabUtil";
import FailUI from "./FailUI";
import GameUI from "./GameUI";

const {ccclass, property} = cc._decorator;

@ccclass
export default class StepOverUI extends UIbase {

    private static _inst: StepOverUI;

    public static get inst() {
        if (this._inst == null) {
            let prefab = PrefabUtil.get("StepOverUI");
            if (!prefab) {
                console.error("StepOverUI预制体未找到");
                return null;
            }
            let v = cc.instantiate(prefab);

            this._inst = v.getComponent(StepOverUI);
        }

        return this._inst;
    }

    private isAutoPlaying: boolean = false; // 自动玩状态
    private autoClickTimer: number = null; // 自动点击定时器

    start () {

    }

    public showUI(data?: any): void {
        super.showUI(data)

        // 接收自动玩状态参数
        this.isAutoPlaying = data || false;
        console.log("StepOverUI显示，自动玩状态:", this.isAutoPlaying);

        // 如果是自动玩模式，3秒后自动重试
        if (this.isAutoPlaying) {
            console.log("自动玩模式：3秒后自动重试当前关卡");
            this.autoClickTimer = setTimeout(() => {
                console.log("自动玩：模拟点击取消按钮进入FailUI");
                this.onClickCancel();
            }, 3000);
        }
    }

    protected onShow(): void {
        Plat.showBanner()
    }

    protected onHide(): void {
        Plat.hideBanner()

        // 清理自动点击定时器
        if (this.autoClickTimer) {
            clearTimeout(this.autoClickTimer);
            this.autoClickTimer = null;
        }
    }

    onClickAdd(){
        Plat.showRewardVideo((success:boolean)=>{
            if(success){
                GameUI.inst.addStepCount()
                this.hideUI()
            }
        })
    }

    onClickCancel(){
        this.hideUI()
        // 传递自动玩状态给FailUI
        FailUI.inst.showUI(this.isAutoPlaying);
    }

}
