// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

@ccclass
export default class ComboUI extends cc.Component {
    @property(cc.ProgressBar)
    bar: cc.ProgressBar = null;

    @property(cc.Label)
    label: cc.Label = null;
    @property(cc.Label)
    count: cc.Label = null;

    @property(cc.Node)
    tip: cc.Node = null;

    @property(cc.Node)
    progressContainer: cc.Node = null;

    comboCount = 0;

    tween: cc.Tween = null;

    private static instance: ComboUI;

    public static getInstance(): ComboUI {
        if (!ComboUI.instance) {
            ComboUI.instance = new ComboUI();
        }
        return ComboUI.instance;
    }

    private constructor() {
        super();
    }

    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    start() {
        this.resetUi()
    }

    resetUi() {
        this.comboCount = 0;

        this.tween = null;

        this.bar.progress = 1;

        if (this.tip) {
            this.tip.active = true
        }
        if (this.progressContainer) {
            this.progressContainer.active = false
        }
        if (this.count) {
            this.count.string = '3'
        }
    }

    updateProgressBar() {
        //连击
        if (this.tween != null) {
            this.comboCount++

            if(this.comboCount == 3)
            {
                if (this.tip) {
                    this.tip.active = false
                }
                if (this.progressContainer) {
                    this.progressContainer.active = true
                }
            }

            this.updateComboLabel()
            this.tween.stop();
            this.resetTimer()
            return;
        }
        //开始记录
        this.resetTimer()

    }

    resetTimer() {
        //重新计时

        this.tween = cc.tween(this.bar)
            .to(0.2,{progress:1})
            .to(5, { progress: 0 })
            .call(() => {
                this.resetUi()
            })
            .start()
    }

    updateComboLabel() {
        if (this.label && this.comboCount >= 3) {
            cc.tween(this.label.node)
            .to(0.5, { scaleX: 1.1, scaleY: 1.2 })
            .to(0.5, { scale: 1 })
            .start()

            if(this.count)
            {
                this.count.string = this.comboCount.toString()

                cc.tween(this.count.node)
                .to(0.5, { scaleX: 1.5, scaleY: 1.7 })
                .to(0.5, { scale: 1 })
                .start()
            }

        }
    }

    // update (dt) {}
}
