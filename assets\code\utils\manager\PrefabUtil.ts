
export default class PrefabUtil
{


    public static objs={};

    //加载预制体
    public static load(call)
    {

        cc.resources.loadDir("./prefab/", cc.Prefab, (err, assets : cc.Prefab[])=>{

           if(assets!=null)
           {
                for(let i=0;i<assets.length;i++)
                {
                    this.objs[assets[i].name]=assets[i];
                }

                call();
           } else {
               console.error("预制体加载失败:", err);
               call();
           }

        })
     
      
    
    }

    public static get(name:string):cc.Prefab
    {

        if(this.objs.hasOwnProperty(name)==false)
        {
            console.error("没有找到预制体---"+name);
            return null;
        }

        return this.objs[name];
    }
}