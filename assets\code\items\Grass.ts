const {ccclass, property} = cc._decorator;
import GameUtil from '../utils/util/GameUtil';

@ccclass
export default class Grass extends cc.Component
 {

    private index:number=0;

    public next=GameUtil.randomRange(60,500);

    protected update(dt: number): void {

        this.index++;

        if(this.index>this.next)
        {
            this.index=0;

            this.next=GameUtil.randomRange(600,1000);
        }
        
        if(this.index>this.next-5)
        {
            this.node.scale=1.2
        }
        else
        {
            this.node.scale=1;
        }
        
    }
}