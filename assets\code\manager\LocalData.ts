

import Plat from "../utils/Palt";
import GameUtil from "../utils/util/GameUtil";
import { BuffItems, GameSetting, skin } from "../datas/Constants";
const { ccclass, property } = cc._decorator;

@ccclass("LocalManager")


export default class LocalData {

    //#region  基本函数

    public static str_datas: { [key: string]: string } = {};

    public static getItemToStr(key, val?: string): string {
        if (this.str_datas[key] == null) {
            let str = cc.sys.localStorage.getItem(key);

            if (str == null || str == "") {
                str = "";

                if (val) {
                    str = val;
                }

                this.str_datas[key] = str;
            }
            else {
                this.str_datas[key] = str;
            }
        }

        return this.str_datas[key];
    }

    public static setItemToStr(key: string, val: string) {
        this.str_datas[key] = val;
        cc.sys.localStorage.setItem(key, val);
    }


    public static int_datas: { [key: string]: number } = {};

    public static getItemToNumber(key, val?: number): number {
        if (this.int_datas[key] == null) {
            let str = cc.sys.localStorage.getItem(key);

            if (str == null || str == "") {
                let v = 0;

                if (val) {
                    v = val;
                }

                this.int_datas[key] = v;
            }
            else {
                this.int_datas[key] = parseInt(str);
            }
        }

        return this.int_datas[key];

    }


    public static setItemToNumber(key: string, val: number): any {
        this.int_datas[key] = val;
        cc.sys.localStorage.setItem(key, val + "");
    }

    //#endregion

    public static yx: boolean = true;
    public static yy: boolean = true;


    public static set lv(curLevel) {
        Plat.setRank(curLevel)
        this.setItemToNumber("curLevel", curLevel)
    }

    public static get lv() {
        return this.getItemToNumber("curLevel", 1);
    }


    public static set openid(id) {
        this.setItemToStr("openid", id);
    }

    public static get openid() {
        return this.getItemToStr("openid");
    }


    private static _userinfo: any = null;

    public static set userinfo(info) {
        this._userinfo = info;

        this.setItemToStr("userinfo", JSON.stringify(info));
    }

    public static get userinfo() {
        if (this._userinfo == null) {
            let str = this.getItemToStr("userinfo");

            if (str == "") {
                this._userinfo = null;
            }
            else {
                this._userinfo = JSON.parse(str);
            }
        }

        return this._userinfo;
    }


    public static set userid(id) {
        this.setItemToStr("userid", id);
    }

    public static get userid() {
        let str = this.getItemToStr("userid");

        if (str == "") {
            str = "" + Date.now() + GameUtil.randomRange(0, 10000000);
            this.setItemToStr("userid", str);
        }

        return str;
    }

    public static get catSkinList(): number[] {
        let skinListStr = localStorage.getItem("local_data_list");
        if (!skinListStr) {
            skinListStr = "{}"
        }
        const skinList = JSON.parse(skinListStr)
        return skinList
    }

    public static set catSkinList(v: number[]) {

    }

    public static set userRegional(regional:string){
        this.setItemToStr("userRegional", regional);
    }

    public static get userRegional():string{
        let userRegional = this.getItemToStr("userRegional", "hongkong");
        return userRegional;
    }

    public static set currentSkin(skin:string){
        this.setItemToStr("currentSkin",skin);
    } 

    //默认rabbit
    public static get currentSkin():string{
        if(this.getItemToStr("currentSkin") == ''){
            this.setItemToStr('currentSkin', 'rabbit');
        }
        return this.getItemToStr("currentSkin");
    }

    //默认rabbit
    public static get obtainedSkins():Array<string>{
        if(this.getItemToStr("obtainedSkins") == '' )
        {
            this.setItemToStr("obtainedSkins", JSON.stringify(["rabbit"]));
        }
        return JSON.parse(this.getItemToStr("obtainedSkins"))
    }

    public static set obtainedSkins(skin:skin){
        let skins = this.obtainedSkins.concat([skin.id])
        this.setItemToStr("obtainedSkins", JSON.stringify(skins))
    }

    //游戏背景
    public static get GameBgId():string{
        if(this.getItemToStr("GameBgId") == ''){
            this.setItemToStr('GameBgId', 'bg1');
        }
        return this.getItemToStr("GameBgId");
    }

    public static set GameBgId(id:string)
    {
        this.setItemToStr('GameBgId', id);
    }

    public static getBuffItemCount(item:BuffItems):number{
        let key = this.getBuffItemKey(item);
        if(key)
        {
            return this.getItemToNumber(key, 0)
        }else{
            console.error('can not get buff item key!')
            return 0;
        }
    }

    static getBuffItemKey(item: BuffItems):string {
        switch (item) {
            case BuffItems.AddTime:
                return 'AddTime'
                break;
            case BuffItems.Inverse:
                return 'Inverse'
                break;
            case BuffItems.Hint:
                return 'Hint'
                break;
            case BuffItems.Undo:
                return 'Undo'
                break;
            case BuffItems.SwapAnimal:
                return 'SwapAnimal'
                break;

            default:
                return null;
                break;
        }
    }

    public static addBuffItemCount(item:BuffItems)
    {
        let key = this.getBuffItemKey(item);
        let count:number = 0;
        if(key)
        {
            switch (item) {
                case BuffItems.AddTime:
                    count =  GameSetting.addTImeReward
                    break;
                case BuffItems.Inverse:
                    count = GameSetting.inverseReward
                    break;
                case BuffItems.Hint:
                    count = GameSetting.hintReward
                    break;
                case BuffItems.Undo:
                    count = GameSetting.undoReward
                    break;
                case BuffItems.SwapAnimal:
                    count = GameSetting.SwapAnimalReward
                    break;
    
                default:
                    break;
            }
            this.setItemToNumber(key,count);
        }else{
            console.error('can not get buff item key!')
        }
    }

    public static consumeBuffItem(item:BuffItems)
    {
        let key = this.getBuffItemKey(item)
        let count = this.getItemToNumber(key)
        if(count>0 && key)
        {
            this.setItemToNumber(key,count)
        }else{
            console.error('can not get buff item key or count less than zero!')
        }
    }

    /**
     * VIP用户状态
     */
    public static get isVipUser(): boolean {
        return this.getItemToNumber("isVipUser", 0) === 1;
    }

    public static set isVipUser(value: boolean) {
        this.setItemToNumber("isVipUser", value ? 1 : 0);
    }

    /**
     * 已使用的邀请码
     */
    public static get usedRedeemCode(): string {
        return this.getItemToStr("usedRedeemCode", "");
    }

    public static set usedRedeemCode(code: string) {
        this.setItemToStr("usedRedeemCode", code);
    }
}
