// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import mySkinData from "../datas/mySkinData";

const {ccclass, property} = cc._decorator;

@ccclass
export default class taskProgressUI extends cc.Component {

    @property(cc.Sprite)
    SpriteNode: cc.Sprite = null;

    @property(cc.Sprite)
    SpriteNodeInShadow: cc.Sprite = null;

    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    onEnable () {
        let skin = mySkinData.findMinOrderElementNotInSkins()

        if(skin)
        {
            this.updateSprite(skin)
        }
    }

    updateSprite(skin)
    {
        let imgPath = '/skins/' + skin.path

        cc.resources.load(imgPath, cc.SpriteFrame, (e, spriteFrame) => {
            if (e) {
                console.log(e)
            } else {
                this.SpriteNode.spriteFrame = spriteFrame
                this.SpriteNodeInShadow.spriteFrame = spriteFrame
            }
        })
    }
    // update (dt) {}
}
