// Learn TypeScript:
import Zh from './language/Zh';
import En from './language/En';
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
import executeInEditMode = cc._decorator.executeInEditMode;
const {ccclass, property} = cc._decorator;

@ccclass
export default class I18n 
 {



   public static language:string="en";
   private static obj:any;

   public static ready:boolean=false;

   public static init(l:string)
   {

      this.ready=true;
      this.language=  l;

      if(this.obj==null)
      {
        this.obj={};
        this.obj.zh=Zh.zh;
        this.obj.en=En.en;
      }       
   }

   public static t(key)
   {

        if(this.ready==false)
        {
            this.init(this.language);
        }


       let data=this.obj[this.language][key];

       if(data==null)
       {
         return "";
       }

       return data;
   }


   // 更新场景渲染器
   public static updateSceneRenderers() 
   { 
      let rootNodes = cc.director.getScene()!.children;
    // walk all nodes with localize label and update
      let allLocalizedLabels: any[] = [];
    // 遍历所有节点获取LocalizedLabel组件
    for (let i = 0; i < rootNodes.length; ++i) {
      let labels = rootNodes[i].getComponentsInChildren('I18nLable');
      allLocalizedLabels = allLocalizedLabels.concat(labels);
    }
    // 更新内容显示
    for (let i = 0; i < allLocalizedLabels.length; ++i) {
      let label = allLocalizedLabels[i];
      if(!label.node.active)continue;
      label.updateLabel();
    }
   
  }
}
