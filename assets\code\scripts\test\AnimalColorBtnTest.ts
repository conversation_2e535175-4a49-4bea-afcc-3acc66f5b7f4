const { ccclass, property } = cc._decorator;

/**
 * AnimalColorBtn测试脚本
 * 用于调试按钮功能问题
 */
@ccclass
export default class AnimalColorBtnTest extends cc.Component {

    start() {
        // 延迟3秒后开始测试
        this.scheduleOnce(() => {
            this.runTests();
        }, 3);
    }

    private runTests() {
        cc.log("========== AnimalColorBtn 测试开始 ==========");
        
        // 查找AnimalColorBtn组件
        const gameUI = cc.find("Canvas/GameUI");
        if (!gameUI) {
            cc.error("未找到GameUI节点");
            return;
        }

        const animalColorBtnNode = cc.find("AnimalColorBtn", gameUI);
        if (!animalColorBtnNode) {
            cc.error("未找到AnimalColorBtn节点");
            return;
        }

        const animalColorBtn = animalColorBtnNode.getComponent("AnimalColorBtn");
        if (!animalColorBtn) {
            cc.error("未找到AnimalColorBtn组件");
            return;
        }

        cc.log("找到AnimalColorBtn组件，开始测试...");
        
        // 调用测试方法
        if (animalColorBtn.testButtonFunction) {
            animalColorBtn.testButtonFunction();
        } else {
            cc.error("testButtonFunction方法不存在");
        }

        // 每5秒自动测试一次
        this.schedule(() => {
            cc.log("========== 自动测试 ==========");
            if (animalColorBtn && animalColorBtn.testButtonFunction) {
                animalColorBtn.testButtonFunction();
            }
        }, 5);
    }

    // 手动触发测试（可以在控制台调用）
    public manualTest() {
        this.runTests();
    }
}
