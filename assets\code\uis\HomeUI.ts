const { ccclass, property } = cc._decorator;
import PrefabUtil from '../utils/manager/PrefabUtil';
import UIbase from '../utils/UIbase';
import GameUI from './GameUI';
import Plat from '../utils/Palt';
import SettingUI from './SettingUI';
import ShopUI from './ShopUI';
import rankUI from './rankUI';
import regionalUI from './regionalUI';
import AudioMgr from '../manager/AudioMgr';
import AudioPath from '../datas/AudioPath';
import ChooseAreaUI from './ChooseAreaUI';
import SkinGalleryUI from './SkinGalleryUI';
import LocalData from '../manager/LocalData';
import provincesData from '../datas/provincesData';
import AnimalYard from '../scripts/item/AnimalYard';
import TipsUI from './TipsUI';
import provinceBtn from '../scripts/item/provinceBtn';


@ccclass
export default class HomeUI extends UIbase {

    private static _inst: HomeUI;

    @property(cc.Node)
    provinceContainer: cc.Node = null;

    @property(cc.Prefab)
    yardPre: cc.Prefab = null;

    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;

    private userSkinInYard: AnimalYard = null;

    public static get inst() {
        if (this._inst == null) {
            let prefab = PrefabUtil.get("HomeUI");
            if (!prefab) {
                console.error("HomeUI预制体未找到");
                return null;
            }
            let v = cc.instantiate(prefab);
            if (!v) {
                console.error("预制体实例化失败");
                return null;
            }
            this._inst = v.getComponent(HomeUI);
        }

        return this._inst;
    }

    protected onLoad(): void {
        this.scrollToPoint();

        //用户更换皮肤之后
        cc.systemEvent.on('selectSkin', (data) => {
            let skinId = data.id;
            if (skinId && this.userSkinInYard) {
                this.userSkinInYard.changeSkin()
            }
        })
        //用户更换地区之后
        cc.systemEvent.on('provinceSelect', (script: provinceBtn) => {
            this.showProvinceRank();
            this.scrollToPoint()
        })

        //生成地区分数数据
        provincesData.generateRegionTeamScores()
    }

    protected onShow(): void {
        Plat.showInterstitialAd();
        Plat.showMoban();
        // Plat.showBanner();
        SkinGalleryUI.inst.initializeData();
        this.showProvinceRank();
        AudioMgr.playBgm();
    }

    scrollToPoint() {

        if (LocalData.userRegional == '') {
            this.scheduleOnce(()=>{
                this.scrollView.scrollToPercentVertical(1);
            },0.1)
        } else {
            this.scheduleOnce(this.onclickToMyPosition, 0.1)
        }
    }

    notifyComingSoon() {
        TipsUI.inst.showTips("建设中……")
    }

    showProvinceRank() {
        const provincesArray = provincesData.getProvinceRank()

        if (!this.yardPre) {
            return;
        }
        this.provinceContainer.removeAllChildren();
        for (let i = 0; i < provincesArray.length; i++) {

            let yard = cc.instantiate(this.yardPre);
            let script = yard.getComponent('AnimalYard');
            if (!script) {
                return;
            }
            script.setRank(i + 1)
            script.setProvinceName(provincesArray[i].name);
            script.setScore(provincesArray[i].score);

            this.provinceContainer.addChild(yard);
            //最后显示用户的皮肤，不然显示不正确
            if (LocalData.userRegional == provincesArray[i].id) {
                this.userSkinInYard = script;
                script.pickUserAnimal();
                script.changeSkin();
                script.switchActiveOfPoint();
            }
        }


    }

    onclickToMyPosition() {
        if (this.scrollView) {
            this.scrollView.stopAutoScroll()
            this.scheduleOnce(()=>{
                if(typeof wx === 'undefined')
                {
                    this.scrollView.scrollToPercentVertical(provincesData.findUserProvincePercentage())
                }else{
                    this.scrollView.scrollToPercentVertical(provincesData.findUserProvincePercentage())
                }
            },0.1)
        }
    }

    protected onHide(): void {
        Plat.hideBanner()
    }

    public onClickStartGame() {
        this.hideUI();
        GameUI.inst.onStartGame();

        // 从主页开始游戏时，重置颜色模式为初始状态
        if (GameUI.inst.animalColorBtn) {
            GameUI.inst.animalColorBtn.resetColorMode();
        }

        AudioMgr.playSound(AudioPath.CLICK);
    }


    public onClickShare() {
        Plat.shareAppMessage();
        AudioMgr.playSound(AudioPath.CLICK);
    }

    public onClickRank() {
        rankUI.inst.showUI();
        AudioMgr.playSound(AudioPath.CLICK);
    }

    public onClickProvinceRank() {
        regionalUI.inst.showUI();
        AudioMgr.playSound(AudioPath.CLICK);
    }

    public onClickSettingBtn() {
        SettingUI.inst.showUI();
        AudioMgr.playSound(AudioPath.CLICK);
    }

    public onClickChooseAreaBtn() {
        ChooseAreaUI.inst.showUI();
        AudioMgr.playSound(AudioPath.CLICK);
    }

    onClickShop() {
        ShopUI.inst.showUI();
        AudioMgr.playSound(AudioPath.CLICK);
    }

    onClickSkinGallery() {
        SkinGalleryUI.inst.showUI();
        AudioMgr.playSound(AudioPath.CLICK);
    }

    public onJump(event, appid) {
        if (Plat.pt.navigateToMiniProgram != null) {
            Plat.pt.navigateToMiniProgram({
                "appId": appid
            });
        }
    }

}