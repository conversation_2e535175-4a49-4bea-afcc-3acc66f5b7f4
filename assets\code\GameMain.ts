
const {ccclass, property} = cc._decorator;
import HomeUI from './uis/HomeUI';


@ccclass
export default class GameMain extends cc.Component {

   

    @property(cc.Node)
    catContainer:cc.Node=null;


    onLoad(): void
    {
        const homeUI = HomeUI.inst;
        if (homeUI) {
            homeUI.showUI();
        } else {
            console.error("HomeUI实例创建失败，无法启动游戏");
        }
    }


    start () {

    }




    

}