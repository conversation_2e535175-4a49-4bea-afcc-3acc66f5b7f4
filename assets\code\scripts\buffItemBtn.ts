// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import AudioPath from "../datas/AudioPath";
import { BuffItems } from "../datas/Constants";
import AudioMgr from "../manager/AudioMgr";
import LocalData from "../manager/LocalData";
import BuffItemUI from "../uis/BuffItemUI";
import GameUI from "../uis/GameUI";

const { ccclass, property } = cc._decorator;

@ccclass
export default class buffItemBtn extends cc.Component {

    @property(cc.Node)
    ad: cc.Node = null;

    @property(cc.Node)
    buffItem: cc.Node = null;

    @property(cc.Label)
    count: cc.Label = null;

    @property({
        type:cc.Enum(BuffItems),
        tooltip: '选一个吧'
    })
    identifier: BuffItems = null;

    // LIFE-CYCLE CALLBACKS:

    onLoad() {
        if (this.identifier != null && this.ad && this.buffItem && this.count) {
            this.init()
        } else {
            console.log(this.identifier,this.ad,this.buffItem,this.count)
            console.error('面板上的预定值为空，请检查面板')
        }
    }

    start() {

    }

    init() {

        if (LocalData.getBuffItemCount(this.identifier) > 9999) {
            if (this.ad) {
                this.ad.active = false;
            }

            if (this.buffItem && this.count) {
                this.buffItem.active = true;

                this.count.string = LocalData.getBuffItemCount(this.identifier).toString()
            }
        } else {
            if (this.ad) {
                this.ad.active = true;
            }
            if (this.buffItem) {
                this.buffItem.active = false;
            }
        }
    }

    // update (dt) {}

    onClickAdReward() {
        
        let count = LocalData.getBuffItemCount(this.identifier);
        if (count > 0) {
            //use buffitem
            // console.log(this.cat_info_list.length);
            GameUI.inst.useBuffItem(this);

        } else if (count == 0) {
            GameUI.inst.is_game_pause = true;
            AudioMgr.playSound(AudioPath.CLICK);
            BuffItemUI.inst.showUI(this)
        }

    }
}
