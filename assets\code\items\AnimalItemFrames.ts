const { ccclass, property } = cc._decorator;

/**
 * 使用单独图片帧的动物组件
 * 适用于不使用图集，而是使用多个单独图片文件的情况
 */
@ccclass
export default class AnimalItemFrames extends cc.Component {

    @property(cc.Node)
    animalNode: cc.Node = null;

    @property(cc.Node)
    starNode: cc.Node = null;

    @property({
        displayName: "动物类型",
        tooltip: "动物的类型名称，如：cat, panda, rabbit等"
    })
    animalType: string = "cat";

    @property({
        displayName: "是否彩色模式",
        tooltip: "是否使用彩色版本的图片"
    })
    isColorMode: boolean = false;

    private frameCount: number = 9;
    private currentFrames: cc.SpriteFrame[] = [];

    start() {
        this.loadAnimalFrames();
    }

    /**
     * 加载动物帧图片
     */
    loadAnimalFrames() {
        const basePath = this.getBasePath();
        const framePromises = [];

        // 加载1-9帧
        for (let i = 1; i <= this.frameCount; i++) {
            const framePath = `${basePath}/${i}`;
            framePromises.push(this.loadSingleFrame(framePath));
        }

        // 加载hit帧
        const hitPath = `${basePath}/hit`;
        framePromises.push(this.loadSingleFrame(hitPath));

        // 等待所有帧加载完成
        Promise.all(framePromises).then((frames) => {
            this.currentFrames = frames.filter(frame => frame !== null);
            this.createAnimation();
        });
    }

    /**
     * 获取图片基础路径
     */
    private getBasePath(): string {
        const colorFolder = this.isColorMode ? "color" : "normal";
        return `animals/frames/${this.animalType}/${colorFolder}`;
    }

    /**
     * 加载单个帧
     */
    private loadSingleFrame(path: string): Promise<cc.SpriteFrame> {
        return new Promise((resolve) => {
            cc.resources.load(path, cc.SpriteFrame, (error, spriteFrame) => {
                if (error) {
                    console.warn(`加载帧失败: ${path}`, error);
                    resolve(null);
                } else {
                    resolve(spriteFrame);
                }
            });
        });
    }

    /**
     * 创建动画
     */
    private createAnimation() {
        if (!this.animalNode || this.currentFrames.length === 0) {
            return;
        }

        const animation = this.animalNode.getComponent(cc.Animation);
        if (!animation) {
            return;
        }

        // 清除现有动画
        animation.removeClip("anim_idle");
        animation.removeClip("anim_collision");

        // 创建空闲动画（使用前9帧）
        const idleFrames = this.currentFrames.slice(0, this.frameCount);
        if (idleFrames.length > 0) {
            const idleClip = cc.AnimationClip.createWithSpriteFrames(idleFrames, 6);
            idleClip.name = "anim_idle";
            idleClip.wrapMode = cc.WrapMode.Loop;
            
            animation.addClip(idleClip);
            animation.play('anim_idle');
        }

        // 创建碰撞动画
        this.createCollisionAnimation(animation);
    }

    /**
     * 创建碰撞动画
     */
    private createCollisionAnimation(animation: cc.Animation) {
        const collisionFrames = [];
        
        // 使用hit帧（如果存在）
        const hitFrame = this.currentFrames[this.frameCount]; // 第10个元素是hit帧
        if (hitFrame) {
            // 重复hit帧8次
            for (let i = 0; i < 8; i++) {
                collisionFrames.push(hitFrame);
            }
        }

        // 添加恢复帧（前2帧）
        for (let i = 0; i < 2 && i < this.currentFrames.length; i++) {
            collisionFrames.push(this.currentFrames[i]);
        }

        if (collisionFrames.length > 0) {
            const collisionClip = cc.AnimationClip.createWithSpriteFrames(collisionFrames, 6);
            collisionClip.name = "anim_collision";
            collisionClip.wrapMode = cc.WrapMode.Normal;
            collisionClip.speed = 1;
            
            animation.addClip(collisionClip);
        }
    }

    /**
     * 切换颜色模式
     */
    public switchColorMode(isColor: boolean) {
        if (this.isColorMode !== isColor) {
            this.isColorMode = isColor;
            this.loadAnimalFrames(); // 重新加载图片
        }
    }

    /**
     * 切换动物类型
     */
    public switchAnimalType(animalType: string) {
        if (this.animalType !== animalType) {
            this.animalType = animalType;
            this.loadAnimalFrames(); // 重新加载图片
        }
    }

    /**
     * 播放碰撞动画
     */
    public playCollisionAnimation() {
        const animation = this.animalNode.getComponent(cc.Animation);
        if (!animation) {
            return;
        }

        animation.on('stop', this.onCollisionAnimationStop, this);
        animation.play('anim_collision');

        if (this.starNode) {
            this.starNode.active = true;
            cc.tween(this.starNode)
                .to(1, { angle: 360 * 2 })
                .call(() => {
                    this.starNode.active = false;
                    this.starNode.angle = 0;
                })
                .start();
        }
    }

    /**
     * 碰撞动画结束回调
     */
    private onCollisionAnimationStop() {
        const animation = this.animalNode.getComponent(cc.Animation);
        if (animation) {
            animation.off('stop', this.onCollisionAnimationStop, this);
            animation.play('anim_idle');
        }
    }

    /**
     * 随机加载动物
     */
    public loadAnimalRandomly() {
        const animalTypes = ['cat', 'panda', 'pig', 'rabbit', 'sheep'];
        const randomType = animalTypes[Math.floor(Math.random() * animalTypes.length)];
        this.switchAnimalType(randomType);
    }
}
