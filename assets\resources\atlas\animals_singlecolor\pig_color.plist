<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{256,430}</string>
                <key>spriteSourceSize</key>
                <string>{256,430}</string>
                <key>textureRect</key>
                <string>{{0,0},{256,430}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{259,430}</string>
                <key>spriteSourceSize</key>
                <string>{259,430}</string>
                <key>textureRect</key>
                <string>{{259,0},{259,430}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{256,439}</string>
                <key>spriteSourceSize</key>
                <string>{256,439}</string>
                <key>textureRect</key>
                <string>{{518,0},{256,439}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>4.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{251,430}</string>
                <key>spriteSourceSize</key>
                <string>{251,430}</string>
                <key>textureRect</key>
                <string>{{777,0},{251,430}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>5.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{251,439}</string>
                <key>spriteSourceSize</key>
                <string>{251,439}</string>
                <key>textureRect</key>
                <string>{{0,439},{251,439}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>6.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{259,439}</string>
                <key>spriteSourceSize</key>
                <string>{259,439}</string>
                <key>textureRect</key>
                <string>{{259,439},{259,439}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>7.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{258,430}</string>
                <key>spriteSourceSize</key>
                <string>{258,430}</string>
                <key>textureRect</key>
                <string>{{518,439},{258,430}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>hit.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{258,439}</string>
                <key>spriteSourceSize</key>
                <string>{258,439}</string>
                <key>textureRect</key>
                <string>{{777,439},{258,439}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>pig_color.png</string>
            <key>size</key>
            <string>{1036,878}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:pig_custom_frames$</string>
            <key>textureFileName</key>
            <string>pig_color.png</string>
        </dict>
    </dict>
</plist>
