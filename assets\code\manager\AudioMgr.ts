// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import LocalData from "./LocalData";

const {ccclass, property} = cc._decorator;

@ccclass
export default class AudioMgr
 {

    public static sounds:any={};

    public static playSound(name)
    {
        if(LocalData.yx==false)
        {
            return;
        }

        if(this.sounds.hasOwnProperty(name)==true)
        {
            cc.audioEngine.playEffect(this.sounds[name],false);
        }
        else
        {
            cc.resources.load("sound/"+name, cc.AudioClip, null, (err, clip:cc.AudioClip) => {
                
                if(clip!=null)
                {
                    this.sounds[name]=clip;

                    cc.audioEngine.playEffect(clip, false);
                }
               
            });
        }
    }


    private static bgm_name="soundBg";

    public static playBgm()
    {

        if(LocalData.yy==false)
        {
            return;
        }


        if(this.sounds.hasOwnProperty(this.bgm_name)==true)
        {
            cc.audioEngine.playMusic(this.sounds[this.bgm_name],true);
        }
        else
        {
            cc.resources.load("sound/"+this.bgm_name, cc.AudioClip, null, (err, clip:cc.AudioClip) => {
                
                if(clip!=null)
                {
                    this.sounds[this.bgm_name]=clip;

                    cc.audioEngine.playMusic(clip, true);
                }
               
            });
        }
    }
    public static stopBgm()
    {
        cc.audioEngine.stopMusic();
    }

}
