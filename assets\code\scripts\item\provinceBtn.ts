import LocalData from "../../manager/LocalData";

const {ccclass, property} = cc._decorator;

@ccclass
export default class provinceBtn extends cc.Component {

    @property(cc.Label)
    provinceName: cc.Label = null;

    @property
    _provinceID = ''

    @property(cc.Sprite)
    province:cc.Sprite = null;

    @property(cc.SpriteFrame)
    selectedButtonFrame:cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    normalButtonFrame:cc.SpriteFrame = null;

    
    public updateUi(name:string, id:string)
    {
        this.provinceName.string = name;

        this._provinceID = id;
    }

    public getID()
    {
        return this._provinceID;
    }

    public isClicked()
    {
        LocalData.userRegional = this._provinceID;

        // let e = new cc.Event.EventCustom('provinceSelect', true)
        // e.setUserData(this)
        // this.node.dispatchEvent(e)

        cc.systemEvent.emit('provinceSelect', this)
    }

    public updateBg()
    {
        if(this.normalButtonFrame && this.selectedButtonFrame){
            let btn = this.province.getComponent(cc.Sprite);
            if(btn && btn.spriteFrame){
                if(btn.spriteFrame == this.normalButtonFrame){
                    btn.spriteFrame = this.selectedButtonFrame
                }else{
                    btn.spriteFrame = this.normalButtonFrame
                }
            }
        }
    }



}