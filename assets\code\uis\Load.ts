import Plat from "../utils/Palt";
import SubLoad from "../utils/SubLoad";
import PrefabUtil from "../utils/manager/PrefabUtil";
import Log from "../utils/util/Log";



const {ccclass, property} = cc._decorator;

@ccclass
export class Load extends cc.Component 
{
   
    @property(cc.Sprite)
    progressBar: cc.Sprite = null;

    @property(cc.Label)
    gameVersion: cc.Label = null;

    @property(cc.Label)
    progressLabel: cc.Label = null;

    
    onLoad () {
        Plat.initMoban();
        // console.log("第一个脚本--加载脚本--这里执行了哈哈哈111");

        // this.gameVersion.string="v_"+Log.game_version+(Log.test==true?"_测试":"");

     //   cc.sys.localStorage.clear();

      //  cc.debug.setDisplayStats(false);

        this.flags[0]=0;

        this.scheduleOnce(this.startLoad,0.2);


    }


    private flags:Array<number>=new Array();

    public startLoad()
    {
        SubLoad.loadSub(()=>{

          
            PrefabUtil.load((()=>{

                // console.warn("预制体全部加载完成");

                cc.director.preloadScene("Main",()=>{
                    
                    // Log.info("main场景预加载成功") 

                    this.flags[0]=1;
                   
                });


            }));
            
           
        })

         Plat.init();

     
     
        // Log.info("---加载开始---")

        
    }

    private p=40;

    private frame_ok:boolean=false;

     update () 
     {
     
        this.p+=0.5;

        if(this.p>98)
        {
            this.p=98;
        }

        if(this.p<10)
        {
            this.p=10;
        }

        this.progressBar.node.width=500*this.p/100;

        this.progressLabel.string="加载资源中... "+Math.floor(this.p)+"%";


        if(this.p>90 && this.frame_ok==false)
        {

            for(var key in this.flags)
            {
                if(this.flags[key] != 1)
                {
                     return;
                }
            }

            cc.director.loadScene("Main",()=>{
                
                // Log.info("main场景加载成功")
            });

        
            this.frame_ok=true;
        }
       

    }
     
}

