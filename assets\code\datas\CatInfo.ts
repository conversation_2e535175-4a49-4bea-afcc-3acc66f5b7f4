

export default class CatInfo
{

    public id:number;

    public now_grids:Array<number>=[]

    public dir:number=1;

    public cat:cc.Node=null;

    public last_grids:Array<Array<number>>=[];

    public move_tween:cc.Tween;

    public lastPosition:cc.Vec2;

    public push_last()
    {
        this.last_grids.push(this.now_grids.concat());
    }

    public revoke()
    {
        this.now_grids = this.last_grids.pop();
    }
}

   
