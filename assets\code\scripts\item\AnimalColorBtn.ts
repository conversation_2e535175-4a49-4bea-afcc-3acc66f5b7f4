import GameUI from "../../uis/GameUI";
import AnimalItem from "../../items/AnimalItem";

const { ccclass, property } = cc._decorator;

@ccclass
export default class AnimalColorBtn extends cc.Component {

    @property(cc.Button)
    colorBtn: cc.Button = null;

    @property(cc.Label)
    btnLabel: cc.Label = null; // 可选，用于显示按钮文字

    @property(cc.Sprite)
    btnIcon: cc.Sprite = null; // 按钮图标

    private isColorMode: boolean = true; // 初始设置为纯色模式
    private originalAtlases: Map<AnimalItem, cc.SpriteAtlas> = new Map();

    // 按钮图标资源
    private colorModeIcon: cc.SpriteFrame = null; // 纯色模式图标
    private multiColorModeIcon: cc.SpriteFrame = null; // 多色模式图标

    // 缓存纯色小猫的图片帧，避免重复加载
    private cachedCatColorFrames: cc.SpriteFrame[] = null;

    // 纯色模式：所有动物都变成纯色小猫
    private colorAtlasMappings = {
        'cat': 'cat_color',
        'panda': 'cat_color',
        'pig': 'cat_color',
        'rabbit': 'cat_color',
        'sheep': 'cat_color'
    };

    start() {
        cc.log("AnimalColorBtn: start() 开始执行");

        // 检查组件绑定情况
        cc.log("AnimalColorBtn: colorBtn绑定状态:", !!this.colorBtn);
        cc.log("AnimalColorBtn: btnLabel绑定状态:", !!this.btnLabel);
        cc.log("AnimalColorBtn: btnIcon绑定状态:", !!this.btnIcon);

        if (this.btnIcon) {
            cc.log("AnimalColorBtn: btnIcon当前spriteFrame:", this.btnIcon.spriteFrame ? this.btnIcon.spriteFrame.name : "null");
        }

        this.loadButtonIcons();
        this.updateBtnUI();

        // 初始化时应用纯色模式
        cc.log("AnimalColorBtn: 初始化时应用纯色模式");
        this.scheduleOnce(() => {
            this.switchAnimalColors();
        }, 0.5); // 延迟0.5秒确保所有动物都已加载

        // 检查按钮组件状态（不再手动绑定事件，使用预制体中的Button配置）
        if (this.colorBtn) {
            cc.log("AnimalColorBtn: 按钮组件已绑定，使用预制体配置的点击事件");
            cc.log("AnimalColorBtn: 按钮可交互状态:", this.colorBtn.interactable);
            cc.log("AnimalColorBtn: 按钮启用状态:", this.colorBtn.enabled);
        } else {
            cc.warn("AnimalColorBtn: colorBtn未正确绑定");
        }
    }

    // 加载按钮图标
    private loadButtonIcons() {
        cc.log("AnimalColorBtn: 开始加载按钮图标");
        let loadedCount = 0;
        const totalIcons = 2;

        // 加载纯色模式图标
        cc.resources.load("other/纯色模式", cc.SpriteFrame, (error, spriteFrame) => {
            if (!error && spriteFrame) {
                this.colorModeIcon = spriteFrame;
                cc.log("AnimalColorBtn: 纯色模式图标加载成功，名称:", spriteFrame.name);
            } else {
                cc.warn("AnimalColorBtn: 纯色模式图标加载失败:", error);
            }

            loadedCount++;
            cc.log(`AnimalColorBtn: 图标加载进度 ${loadedCount}/${totalIcons}`);
            if (loadedCount >= totalIcons) {
                cc.log("AnimalColorBtn: 所有图标加载完成，更新按钮图标");
                this.updateBtnIcon();
            }
        });

        // 加载多色模式图标
        cc.resources.load("other/多色模式", cc.SpriteFrame, (error, spriteFrame) => {
            if (!error && spriteFrame) {
                this.multiColorModeIcon = spriteFrame;
                cc.log("AnimalColorBtn: 多色模式图标加载成功，名称:", spriteFrame.name);
            } else {
                cc.warn("AnimalColorBtn: 多色模式图标加载失败:", error);
            }

            loadedCount++;
            cc.log(`AnimalColorBtn: 图标加载进度 ${loadedCount}/${totalIcons}`);
            if (loadedCount >= totalIcons) {
                cc.log("AnimalColorBtn: 所有图标加载完成，更新按钮图标");
                this.updateBtnIcon();
            }
        });
    }

    public onClick() {
        cc.log(`AnimalColorBtn: ========== 按钮被点击 ==========`);
        cc.log(`AnimalColorBtn: 点击前状态 - isColorMode: ${this.isColorMode}`);
        cc.log(`AnimalColorBtn: 点击前状态 - 按钮可交互: ${this.colorBtn ? this.colorBtn.interactable : 'colorBtn未绑定'}`);

        // 检查游戏状态
        const gameUI = GameUI.inst;
        if (gameUI) {
            cc.log(`AnimalColorBtn: 游戏暂停状态: ${gameUI.is_game_pause}`);
            cc.log(`AnimalColorBtn: 游戏进行状态: ${gameUI.is_game_playing}`);

            if (gameUI.is_game_pause) {
                cc.warn("AnimalColorBtn: 游戏已暂停，忽略点击");
                return;
            }
        }

        // 检查按钮状态
        if (this.colorBtn && !this.colorBtn.interactable) {
            cc.warn("AnimalColorBtn: 按钮当前不可交互，忽略点击");
            return;
        }

        this.isColorMode = !this.isColorMode;
        cc.log(`AnimalColorBtn: 切换后模式: ${this.isColorMode ? '纯色' : '多色'}`);

        // 立即更新按钮UI，提供即时的视觉反馈
        this.updateBtnUI();

        // 强制更新图标
        this.forceUpdateIcon();

        // 然后切换动物颜色
        this.switchAnimalColors();

        // 确保按钮保持可交互状态
        this.ensureButtonInteractable();

        cc.log(`AnimalColorBtn: ========== 按钮点击处理完成 ==========`);
    }

    public switchAnimalColors() {
        // 获取游戏中的所有动物
        const gameUI = GameUI.inst;
        if (!gameUI) {
            return;
        }

        if (!gameUI.cat_info_list) {
            return;
        }

        gameUI.cat_info_list.forEach((catInfo) => {
            const animalItem = catInfo.cat.getComponent(AnimalItem);
            if (!animalItem) {
                return;
            }

            if (this.isColorMode) {
                this.switchToColorMode(animalItem);
            } else {
                this.restoreOriginalAtlas(animalItem);
            }
        });
    }

    private switchToColorMode(animalItem: AnimalItem) {
        // 检查图集是否已准备好
        if (!animalItem.isAtlasReady()) {
            cc.warn(`动物图集还未加载完成，延迟重试`);
            // 如果图集还没加载完成，延迟重试
            this.scheduleOnce(() => {
                this.switchToColorMode(animalItem);
            }, 0.1);
            return;
        }

        // 获取当前图集名称
        const atlasName = animalItem.getCurrentAtlasName();
        cc.log(`原始动物类型: ${atlasName} -> 将变成纯色小猫`);

        // 保存原始图集
        if (!this.originalAtlases.has(animalItem)) {
            this.originalAtlases.set(animalItem, animalItem.getCurrentAtlas());
        }

        // 直接使用cat_color文件夹中的单个图片文件
        this.loadCatColorFrames(animalItem);
    }

    private loadCatColorFrames(animalItem: AnimalItem) {
        // 如果已经缓存了纯色小猫图片帧，直接使用
        if (this.cachedCatColorFrames && this.cachedCatColorFrames.length > 0) {
            cc.log("使用缓存的纯色小猫图片帧");
            this.createCatColorAnimation(animalItem, this.cachedCatColorFrames);
            return;
        }

        // 首次加载纯色小猫图片帧
        const basePath = "atlas/animals_singlecolor/cat_color";
        const framePromises = [];

        // 加载1-9帧
        for (let i = 1; i <= 9; i++) {
            const framePath = `${basePath}/${i}`;
            framePromises.push(this.loadSingleFrame(framePath));
        }

        // 加载hit帧
        const hitPath = `${basePath}/hit`;
        framePromises.push(this.loadSingleFrame(hitPath));

        // 等待所有帧加载完成
        Promise.all(framePromises).then((frames) => {
            const validFrames = frames.filter(frame => frame !== null);
            if (validFrames.length > 0 && animalItem.isValid) {
                // 缓存加载的图片帧
                this.cachedCatColorFrames = validFrames;
                this.createCatColorAnimation(animalItem, validFrames);
                cc.log(`成功加载并缓存纯色小猫图片，共${validFrames.length}帧`);
            } else {
                cc.error(`无法加载纯色小猫图片`);
            }
        });
    }
    
    private loadSingleFrame(path: string): Promise<cc.SpriteFrame> {
        return new Promise((resolve) => {
            cc.resources.load(path, cc.SpriteFrame, (error, spriteFrame) => {
                if (error) {
                    console.warn(`加载帧失败: ${path}`, error);
                    resolve(null);
                } else {
                    resolve(spriteFrame);
                }
            });
        });
    }
    
    private createCatColorAnimation(animalItem: AnimalItem, frames: cc.SpriteFrame[]) {
        if (!animalItem.animalNode) {
            return;
        }
        
        const animation = animalItem.animalNode.getComponent(cc.Animation);
        if (!animation) {
            return;
        }
        
        // 清除现有的动画剪辑
        animation.removeClip("anim_idle");
        animation.removeClip("anim_collision");
        
        // 创建空闲动画（使用前9帧）
        const idleFrames = frames.slice(0, 9);
        if (idleFrames.length > 0) {
            const idleClip = cc.AnimationClip.createWithSpriteFrames(idleFrames, 6);
            idleClip.name = "anim_idle";
            idleClip.wrapMode = cc.WrapMode.Loop;
            
            animation.addClip(idleClip);
            animation.play('anim_idle');
        }
        
        // 创建碰撞动画
        this.createCatColorCollisionAnimation(animation, frames);
    }
    
    private createCatColorCollisionAnimation(animation: cc.Animation, frames: cc.SpriteFrame[]) {
        const collisionFrames = [];
        
        // 添加前2帧作为开始
        for (let i = 0; i < Math.min(2, frames.length); i++) {
            collisionFrames.push(frames[i]);
        }
        
        // 添加hit帧（如果存在）
        const hitFrame = frames[frames.length - 1]; // hit帧是最后一个
        if (hitFrame) {
            // 重复hit帧8次
            for (let i = 0; i < 8; i++) {
                collisionFrames.push(hitFrame);
            }
        }
        
        // 添加恢复帧（前2帧）
        for (let i = 0; i < Math.min(2, frames.length); i++) {
            collisionFrames.push(frames[i]);
        }
        
        if (collisionFrames.length > 0) {
            const collisionClip = cc.AnimationClip.createWithSpriteFrames(collisionFrames, 6);
            collisionClip.name = "anim_collision";
            collisionClip.wrapMode = cc.WrapMode.Normal;
            collisionClip.speed = 1;
            
            animation.addClip(collisionClip);
        }
    }

    private restoreOriginalAtlas(animalItem: AnimalItem) {
        const originalAtlas = this.originalAtlases.get(animalItem);
        if (originalAtlas && animalItem.isValid) {
            cc.log(`恢复动物原始图集: ${originalAtlas.name}`);
            animalItem.setAtlas(originalAtlas);
        } else {
            cc.warn("无法恢复原始图集，原始图集未找到或动物无效");
        }
    }

    private getColorAtlasName(atlasName: string): string {
        const cleanAtlasName = atlasName.replace('.plist', '');
        return this.colorAtlasMappings[cleanAtlasName] || null;
    }

    private updateBtnUI() {
        cc.log("AnimalColorBtn: updateBtnUI() 开始");

        // 检查按钮状态
        if (this.colorBtn) {
            cc.log(`AnimalColorBtn: 按钮状态 - 可交互: ${this.colorBtn.interactable}, 启用: ${this.colorBtn.enabled}`);
        }

        if (this.btnLabel) {
            this.btnLabel.string = this.isColorMode ? "恢复原色" : "纯色模式";
        }
        this.updateBtnIcon();

        // 确保按钮保持可交互
        this.ensureButtonInteractable();

        cc.log("AnimalColorBtn: updateBtnUI() 完成");
    }

    // 更新按钮图标
    private updateBtnIcon() {
        cc.log(`AnimalColorBtn: updateBtnIcon() 开始执行，当前模式: ${this.isColorMode ? '纯色' : '多色'}`);

        if (!this.btnIcon) {
            cc.warn("AnimalColorBtn: btnIcon未设置");
            return;
        }

        cc.log("AnimalColorBtn: btnIcon当前spriteFrame:", this.btnIcon.spriteFrame ? this.btnIcon.spriteFrame.name : "null");
        cc.log("AnimalColorBtn: colorModeIcon状态:", !!this.colorModeIcon);
        cc.log("AnimalColorBtn: multiColorModeIcon状态:", !!this.multiColorModeIcon);

        if (this.isColorMode) {
            // 当前是纯色模式，显示多色模式图标（表示点击后会切换到多色模式）
            if (this.multiColorModeIcon) {
                const oldFrame = this.btnIcon.spriteFrame;
                this.btnIcon.spriteFrame = this.multiColorModeIcon;
                cc.log(`AnimalColorBtn: 图标已更新 ${oldFrame ? oldFrame.name : 'null'} -> ${this.multiColorModeIcon.name}`);
                cc.log("AnimalColorBtn: 显示多色模式图标（当前为纯色模式）");
            } else {
                cc.warn("AnimalColorBtn: 多色模式图标未加载，无法切换图标");
            }
        } else {
            // 当前是多色模式，显示纯色模式图标（表示点击后会切换到纯色模式）
            if (this.colorModeIcon) {
                const oldFrame = this.btnIcon.spriteFrame;
                this.btnIcon.spriteFrame = this.colorModeIcon;
                cc.log(`AnimalColorBtn: 图标已更新 ${oldFrame ? oldFrame.name : 'null'} -> ${this.colorModeIcon.name}`);
                cc.log("AnimalColorBtn: 显示纯色模式图标（当前为多色模式）");
            } else {
                cc.warn("AnimalColorBtn: 纯色模式图标未加载，无法切换图标");
            }
        }

        // 强制刷新显示
        if (this.btnIcon.node) {
            this.btnIcon.node.active = false;
            this.btnIcon.node.active = true;
        }

        // 额外的强制更新方法
        this.scheduleOnce(() => {
            this.forceUpdateIcon();
        }, 0.1);
    }

    // 强制更新图标的方法
    private forceUpdateIcon() {
        if (!this.btnIcon) return;

        cc.log("AnimalColorBtn: 强制更新图标");
        const targetIcon = this.isColorMode ? this.multiColorModeIcon : this.colorModeIcon;

        if (targetIcon) {
            this.btnIcon.spriteFrame = targetIcon;
            cc.log(`AnimalColorBtn: 强制设置图标为: ${targetIcon.name}`);

            // 强制重新渲染
            this.btnIcon.markForRender(true);
        }
    }

    // 确保按钮保持可交互状态
    private ensureButtonInteractable() {
        if (this.colorBtn) {
            const wasInteractable = this.colorBtn.interactable;
            this.colorBtn.interactable = true;
            this.colorBtn.enabled = true;

            cc.log(`AnimalColorBtn: 按钮状态检查 - 之前可交互: ${wasInteractable}, 现在可交互: ${this.colorBtn.interactable}`);

            // 如果按钮之前不可交互，现在重新启用了，记录这个情况
            if (!wasInteractable) {
                cc.warn("AnimalColorBtn: 按钮之前被禁用，现已重新启用");
            }
        }
    }

    // 重置状态（游戏重新开始时调用）
    public resetColorMode() {
        cc.log("AnimalColorBtn: 重置颜色模式状态到初始纯色模式");
        this.isColorMode = true; // 重置为纯色模式
        this.originalAtlases.clear();
        // 保留缓存的纯色小猫图片帧，避免重复加载
        // this.cachedCatColorFrames = null; // 注释掉，保持缓存
        this.updateBtnUI();

        // 立即应用纯色模式到所有动物
        this.switchAnimalColors();
    }

    // 清理数据但保持当前颜色模式状态（进入下一关时调用）
    public clearDataKeepMode() {
        cc.log("AnimalColorBtn: 清理数据但保持当前颜色模式状态");
        cc.log("AnimalColorBtn: 当前模式:", this.isColorMode ? '纯色模式' : '多色模式');

        // 只清理缓存数据，不改变当前模式状态
        this.originalAtlases.clear();

        // 保持当前UI状态
        this.updateBtnUI();

        // 根据当前模式应用到所有动物
        this.switchAnimalColors();
    }

    // 公共测试方法
    public testButtonFunction() {
        cc.log("AnimalColorBtn: 测试按钮功能");
        cc.log("AnimalColorBtn: 当前状态:", this.isColorMode ? '纯色模式' : '多色模式');
        cc.log("AnimalColorBtn: colorModeIcon:", !!this.colorModeIcon);
        cc.log("AnimalColorBtn: multiColorModeIcon:", !!this.multiColorModeIcon);
        cc.log("AnimalColorBtn: btnIcon:", !!this.btnIcon);

        if (this.btnIcon && this.btnIcon.spriteFrame) {
            cc.log("AnimalColorBtn: 当前图标:", this.btnIcon.spriteFrame.name);
        }

        // 强制切换一次
        this.onClick();
    }

    // 组件销毁时清理资源
    onDestroy() {
        // 清理缓存的图片帧
        if (this.cachedCatColorFrames) {
            this.cachedCatColorFrames.clear();
        }
    }
}