// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import LocalData from "../../manager/LocalData";

const { ccclass, property } = cc._decorator;

@ccclass
export default class AnimalYard extends cc.Component {

    @property(cc.Label)
    rank: cc.Label = null;

    @property(cc.Label)
    provinceName: cc.Label = null;

    @property(cc.Sprite)
    signboard:cc.Sprite = null;

    @property(cc.Label)
    score: cc.Label = null;

    @property([cc.SpriteFrame])
    skins: cc.SpriteFrame[] = [];

    @property([cc.SpriteFrame])
    sign: cc.SpriteFrame[] = [];

    // LIFE-CYCLE CALLBACKS:
    private userNode:cc.Node = null;

    onLoad() {
        this.setRandomAnimal()
        this.setSignBoard()
    }

    setSignBoard() {
        if(this.signboard)
        {
            let sprite = this.signboard.getComponent(cc.Sprite)
            if(sprite)
            {
                sprite.spriteFrame = this.sign[Math.floor(Math.random() * this.sign.length)]
            }
        }
    }


    setRandomAnimal() {
        let yard = this.node.getChildByName('yard');
        if (yard) {
            yard.children.forEach(child => {
                const sprite = child.getChildByName('animal').getComponent(cc.Sprite);
                if (sprite) {
                    const randomIndex = Math.floor(Math.random() * this.skins.length);
                    sprite.spriteFrame = this.skins[randomIndex];
                }
            });
        }

    }

    pickUserAnimal() {
        let yard = this.node.getChildByName('yard');
        const children = yard.children;
        const lastFourChildren = children.slice(-4); // 获取最后四个子节点
        if (lastFourChildren.length > 0) {
            const randomIndex = Math.floor(Math.random() * lastFourChildren.length);
            this.userNode = lastFourChildren[randomIndex];
        }

    }

    changeSkin() {
        if (LocalData.currentSkin && this.userNode.getChildByName('animal')) {
            let frame = this.findSpriteFrameByName(LocalData.currentSkin);
            const sprite = this.userNode.getChildByName('animal').getComponent(cc.Sprite);
            if (frame && sprite) {
                sprite.spriteFrame = frame;
            } else {
                console.log('miss user current skin!')
            }
        }
    }
    switchActiveOfPoint() {
        let you = this.userNode.getChildByName('you')
        if (you) {
            you.active = !you.active;
        }
        let pointYou = this.userNode.getChildByName('pointYou')
        if (pointYou) {
            pointYou.active = !pointYou.active;
        }
    }

    // 通过名字查找cc.SpriteFrame
    findSpriteFrameByName(name: string): cc.SpriteFrame | null {
        for (const spriteFrame of this.skins) {
            if (spriteFrame.name === name) {
                return spriteFrame;
            }
        }
        return null; // 如果没有找到，返回null
    }


    start() {

    }

    public setRank(rank: string) {
        if (this.rank) {
            this.rank.string = `第${rank}名`;
        } else {
            console.error('miss rank property')
        }
    }

    public setProvinceName(name: string) {
        if (this.provinceName) {
            this.provinceName.string = `${name}队`;
        } else {
            console.error('miss province name label')
        }
    }

    public setScore(score: string) {
        if (this.score) {
            this.score.string = `${score}分`;
        } else {
            console.error('miss score label')
        }
    }

    // update (dt) {}
}
