<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>1.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{0,0},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>2.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{350,0},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>3.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{700,0},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>4.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{1050,0},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>5.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{1400,0},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>6.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{0,600},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>7.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{350,600},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>8.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{700,600},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>9.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{1050,600},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>hit.PNG</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{0,0}</string>
                <key>spriteSize</key>
                <string>{350,600}</string>
                <key>spriteSourceSize</key>
                <string>{350,600}</string>
                <key>textureRect</key>
                <string>{{1400,600},{350,600}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>sheep.png</string>
            <key>size</key>
            <string>{1750,1200}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:a83df8c40204685e9815c02bc7ca2487:ec22c65df3d06971f4da82e3d619211a:13084b924613a7ef2ca9c6df3339651b$</string>
            <key>textureFileName</key>
            <string>sheep.png</string>
        </dict>
    </dict>
</plist>
