const {ccclass, property} = cc._decorator;
import AudioPath from '../datas/AudioPath';
import mySkinData from '../datas/mySkinData';
import AudioMgr from '../manager/AudioMgr';
import { DataMgr } from '../manager/DataMgr';
import LocalData from '../manager/LocalData';
import PrefabUtil from '../utils/manager/PrefabUtil';
import Plat from '../utils/Palt';
import UIbase from '../utils/UIbase';
import GameUI from './GameUI';
import SkinGalleryUI from './SkinGalleryUI';


@ccclass
export default class WinUI extends UIbase {

    private static _inst:WinUI;

    public static get inst()
    {
        if(this._inst==null)
        {
            let prefab = PrefabUtil.get("WinUI");
            if (!prefab) {
                console.error("WinUI预制体未找到");
                return null;
            }
            let v=cc.instantiate(prefab);

            this._inst=v.getComponent(WinUI);
        }

        return this._inst;
    }

    @property(cc.Sprite)
    cat: cc.Sprite = null;

    private isAutoPlaying: boolean = false; // 自动玩状态
    private autoClickTimer: number = null; // 自动点击定时器
    private backupTimer: number = null; // 备用定时器

    public showUI(data?: any): void {
        super.showUI(data)

        // 接收自动玩状态参数
        this.isAutoPlaying = data || false;
        console.log("=== WinUI显示 ===");
        console.log("接收到的data参数:", data);
        console.log("解析后的自动玩状态:", this.isAutoPlaying);
        console.log("data的类型:", typeof data);
        console.log("data是否为true:", data === true);

        // 如果是自动玩模式，3秒后自动点击下一关
        if (this.isAutoPlaying) {
            console.log("✅ 自动玩模式激活：3秒后自动点击下一关");
            this.autoClickTimer = setTimeout(() => {
                console.log("🎮 自动玩：开始模拟点击下一关按钮");
                if (this.node && this.node.active) {
                    console.log("✅ WinUI界面仍然活跃，执行自动点击");
                    this.onClickStartGame();
                } else {
                    console.warn("❌ WinUI界面已关闭，取消自动点击");
                }
            }, 3000);

            // 备用机制：如果6秒后界面还在，强制点击
            this.backupTimer = setTimeout(() => {
                console.log("🔄 备用机制：检查是否需要强制自动点击");
                if (this.node && this.node.active && this.isAutoPlaying) {
                    console.log("⚠️ 主要机制可能失效，执行备用自动点击");
                    this.onClickStartGame();
                }
            }, 6000);
        } else {
            console.log("❌ 非自动玩模式，不会自动跳转");
        }

        // const id = DataMgr.ins.getRandomId();
        // cc.resources.load("/cat/cat_skin_" + id, cc.SpriteFrame, (error, frame) => {
        //     this.cat.spriteFrame = frame
        // })

        if(GameUI.inst.isRewardSkinLevel == false){
            return;
        }

        let skin = mySkinData.findMinOrderElementNotInSkins()
        if(skin)
        {
            let imgPath = '/skins/' + skin.path

            cc.resources.load(imgPath, cc.SpriteFrame, (e, spriteFrame) => {
                if (e) {
                    console.log(e)
                } else {
                    this.cat.spriteFrame = spriteFrame
                }
            })
            //皮肤处理
            LocalData.obtainedSkins = skin
            SkinGalleryUI.inst.initializeData();
        }
    }

    protected onShow(): void {
        Plat.showBanner()
        // Plat.showInterstitialAd()
    }

    protected onHide(): void {
        Plat.hideBanner()

        // 清理自动点击定时器
        if (this.autoClickTimer) {
            clearTimeout(this.autoClickTimer);
            this.autoClickTimer = null;
        }

        // 清理备用定时器
        if (this.backupTimer) {
            clearTimeout(this.backupTimer);
            this.backupTimer = null;
        }
    }


    public onClickStartGame()
    {
        console.log("=== 点击下一关按钮 ===");
        console.log("当前自动玩状态:", this.isAutoPlaying);
        console.log("GameUI实例存在:", !!GameUI.inst);

        this.hideUI();
        GameUI.inst.onStartGame();

        // 如果之前是自动玩模式，在新关卡开始后恢复自动玩
        if (this.isAutoPlaying) {
            console.log("✅ 自动玩模式：新关卡开始后恢复自动玩");
            // 延迟一点时间确保游戏完全初始化后再开始自动玩
            setTimeout(() => {
                console.log("检查游戏状态 - GameUI存在:", !!GameUI.inst);
                console.log("检查游戏状态 - 游戏正在进行:", GameUI.inst ? GameUI.inst.is_game_playing : "GameUI不存在");

                if (GameUI.inst && GameUI.inst.is_game_playing) {
                    console.log("🎮 开始恢复自动玩功能");
                    GameUI.inst.startAutoPlay();
                    console.log("✅ 自动玩已恢复，状态:", GameUI.inst.isAutoPlaying);
                } else {
                    console.warn("❌ 无法恢复自动玩 - 游戏状态异常");
                }
            }, 1000);
        } else {
            console.log("❌ 非自动玩模式，不恢复自动玩");
        }

        AudioMgr.playSound(AudioPath.CLICK);
    }

    public onClickShare()
    {
        Plat.shareAppMessage();
        AudioMgr.playSound(AudioPath.CLICK);
    }
   
    

}