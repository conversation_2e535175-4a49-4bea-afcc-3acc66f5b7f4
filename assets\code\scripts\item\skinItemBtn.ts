// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { skin } from "../../datas/Constants";
import LocalData from "../../manager/LocalData";
import TipsUI from "../../uis/TipsUI";

const { ccclass, property } = cc._decorator;

@ccclass
export default class skinItemBtn extends cc.Component {

    @property(cc.Sprite)
    grayBG: cc.Sprite = null;

    @property(cc.Sprite)
    charactor: cc.Sprite = null;

    @property(cc.Sprite)
    checkMark: cc.Sprite = null;

    public id: string = null

    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}

    start() {
    }

    public updateUi(item: skin) {
        if (item.id) {
            this.id = item.id
            this.chageSpriteFrame(item.path)
            if (LocalData.obtainedSkins.includes(item.id)) {
                //用户拥有的皮肤
                //取消灰背景
                this.grayBG.node.active = false;
                this.charactor.node.color = cc.color(255,255,255,255);//显示角色
                
                //是否是当前的皮肤
                if (item.isCurrent) {
                    this.checkMark.node.active = true
                    this.sendEvent();
                } else {
                    this.checkMark.node.active = false;
                }
            }else{
                this.grayBG.node.active = true;
                this.charactor.node.color = cc.color(8,7,7,255);//涂黑角色
            }

        }
    }

    chageSpriteFrame(name: string) {
        let imgPath = '/skins/' + name

        cc.resources.load(imgPath, cc.SpriteFrame, (e, spriteFrame) => {
            if (e) {
                console.log(e)
            } else {
                this.charactor.spriteFrame = spriteFrame
            }
        })
    }

    public changeCheckMark() {
        this.checkMark.node.active = !this.checkMark.node.active;
    }

    public onClick(){

        //用户切换皮肤
        if(this.grayBG.node.active == false){

            if(this.checkMark.node.active == false){
                LocalData.currentSkin = this.id;
                this.checkMark.node.active = true;
                this.sendEvent();
                TipsUI.inst.showTips('皮肤已更换')
            }else{
                TipsUI.inst.showTips('已是当前皮肤')
            }

        }
        //点击未取得的皮肤
        else{
            TipsUI.inst.showTips('请赢得更多关卡获得新的皮肤！')
        }
    }

    sendEvent() {
        // let e = new cc.Event.EventCustom('selectSkin', true)
        // e.setUserData(this)
        // this.node.dispatchEvent(e)

        cc.systemEvent.emit('selectSkin', this)
    }

    // update (dt) {}
}
