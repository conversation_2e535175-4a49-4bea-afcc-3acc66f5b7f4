// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { DataMgr } from "../manager/DataMgr";
import GameUI from "../uis/GameUI";
import PrefabUtil from "../utils/manager/PrefabUtil";

const { ccclass, property } = cc._decorator;

@ccclass
export default class AnimalItem extends cc.Component {

    private atlas: cc.SpriteAtlas = null;

    @property(cc.Node)
    animalNode: cc.Node = null

    @property(cc.Node)
    starNode: cc.Node = null

    private atlasDirectory: string = 'atlas/animals/'; // 纹理集文件夹路径

    frameCount: number = 9; //atlas里的动作图片名

    // 添加加载完成标记
    private isAtlasLoaded: boolean = false;
    private pendingColorSwitch: cc.SpriteAtlas = null;

    start() {

    }

    protected onEnable(): void {

        this.loadAnimalRandomly()

    }
    loadAnimalRandomly() {
        cc.resources.loadDir(this.atlasDirectory, (err, assets) => {
            if (err) {
                console.error('Failed to load directory:', err);
                return;
            }

            // 过滤出所有的 SpriteAtlas 资源
            const atlases = assets.filter(asset => asset instanceof cc.SpriteAtlas) as cc.SpriteAtlas[];

            if (atlases.length === 0) {
                console.error('No SpriteAtlas found in the directory.');
                return;
            }

            // 随机选择一个 SpriteAtlas
            this.atlas = atlases[Math.floor(Math.random() * atlases.length)];
            this.isAtlasLoaded = true;

            // 如果有待处理的颜色切换，立即执行
            if (this.pendingColorSwitch) {
                this.setAtlas(this.pendingColorSwitch);
                this.pendingColorSwitch = null;
            } else {
                this.changeCharacterAppearance();
            }
        });
    }

    /**
     * 加载指定的动物类型
     */
    public loadSpecificAnimal(animalName: string) {
        const atlasPath = `${this.atlasDirectory}${animalName}`;
        cc.resources.load(atlasPath, cc.SpriteAtlas, (error, atlas) => {
            if (!error && atlas) {
                this.atlas = atlas;
                this.isAtlasLoaded = true;

                // 如果有待处理的颜色切换，立即执行
                if (this.pendingColorSwitch) {
                    this.setAtlas(this.pendingColorSwitch);
                    this.pendingColorSwitch = null;
                } else {
                    this.changeCharacterAppearance();
                }
            }
        });
    }

    private createSpriteFrames(): cc.SpriteFrame[] {
        const frames: cc.SpriteFrame[] = [];
        for (let i = 1; i <= this.frameCount; i++) {
            // 尝试多种帧名称格式
            const frameNames = [
                i.toString(),           // "1", "2", "3"...
                `${i}.PNG`,            // "1.PNG", "2.PNG", "3.PNG"...
                `${i}.png`             // "1.png", "2.png", "3.png"...
            ];

            let spriteFrame: cc.SpriteFrame = null;
            for (const frameName of frameNames) {
                spriteFrame = this.atlas.getSpriteFrame(frameName);
                if (spriteFrame) {
                    break;
                }
            }

            if (spriteFrame) {
                frames.push(spriteFrame);
            }
        }
        return frames;
    }

    // 更改角色形象的方法
    public changeCharacterAppearance() {
        if (!this.animalNode) {
            return;
        }

        let animation = this.animalNode.getComponent(cc.Animation);
        if (!animation) {
            return;
        }

        // 清除现有的动画剪辑，避免重复添加
        animation.removeClip("anim_idle");
        animation.removeClip("anim_collision");

        // 创建空闲动画
        const idleFrames = this.createSpriteFrames();
        if (idleFrames.length > 0) {
            var idleClip = cc.AnimationClip.createWithSpriteFrames(idleFrames, 6);
            idleClip.name = "anim_idle";
            idleClip.wrapMode = cc.WrapMode.Loop;

            animation.addClip(idleClip);
            animation.play('anim_idle');
        }

        // 创建碰撞动画
        const collisionFrames = this.createCollisonSpriteFrames();
        if (collisionFrames.length > 0) {
            var collisionClip = cc.AnimationClip.createWithSpriteFrames(collisionFrames, 6);
            collisionClip.name = "anim_collision";
            collisionClip.wrapMode = cc.WrapMode.Normal;
            collisionClip.speed = 1;

            animation.addClip(collisionClip);
        }
    }

    createCollisonSpriteFrames(): cc.SpriteFrame[] {
        let frames: cc.SpriteFrame[] = [];

        // 查找hit帧
        const hitFrameNames = ['hit', 'hit.PNG', 'hit.png'];
        let hitFrame: cc.SpriteFrame = null;
        for (const frameName of hitFrameNames) {
            hitFrame = this.atlas.getSpriteFrame(frameName);
            if (hitFrame) {
                break;
            }
        }

        if (hitFrame) {
            frames = frames.concat(...Array(8).fill(hitFrame));
        }

        // 添加恢复帧
        for (let i = 1; i <= 2; i++) {
            const frameNames = [
                i.toString(),           // "1", "2"
                `${i}.PNG`,            // "1.PNG", "2.PNG"
                `${i}.png`             // "1.png", "2.png"
            ];

            let spriteFrame: cc.SpriteFrame = null;
            for (const frameName of frameNames) {
                spriteFrame = this.atlas.getSpriteFrame(frameName);
                if (spriteFrame) {
                    break;
                }
            }

            if (spriteFrame) {
                frames.push(spriteFrame);
            }
        }

        return frames;
    }

    playCollisionAnimation() {
        let animation = this.animalNode.getComponent(cc.Animation);

        animation.on('stop', this.callback, this)

        let s = animation.play('anim_collision')
        if (this.starNode) {

            this.starNode.active = true;
            cc.tween(this.starNode).to(1, { angle: 360 * 2 }).call(() => {
                this.starNode.active = false;
                this.starNode.angle = 0;
            }).start()
        }

    }

    callback(type: string, state: cc.AnimationState) {

        let animation = this.animalNode.getComponent(cc.Animation);
        if (state.name == "anim_collision") {
            animation.play('anim_idle')
        }
    }

    onDestroy() {
        console.log('animal item destroy')

        let animation = this.animalNode.getComponent(cc.Animation);

        animation.off('stop', this.callback, this);
    }

    createTrailEffect() {

        this.schedule(() => {
            // 1. 从对象池中获取节点
            let node: cc.Node;
            if (GameUI.inst.movementEffectPool.size() > 0) {
                node = GameUI.inst.movementEffectPool.get();
            } else {
                let movementEffectPrefab = PrefabUtil.get('MovementEffect');
                if (movementEffectPrefab) {
                    node = cc.instantiate(movementEffectPrefab);
                } else {
                    console.error("MovementEffect预制体未找到");
                    return;
                }
            }

            let angle = 0;
            let effectGap = 50;//角色身后的距离
            GameUI.inst.catContainer.addChild(node)
            let animalPosition = this.node.getPosition()
            let animalLastPosition = this.node.getPosition()
            
            switch (this.node.angle) {
                case 0:
                    angle = -90
                    animalPosition.sub(new cc.Vec2(0, effectGap), animalLastPosition)
                    break
                case 180:
                    angle = 90
                    animalPosition.add(new cc.Vec2(0, effectGap), animalLastPosition)
                    break
                case 90:
                    angle = 0
                    animalPosition.add(new cc.Vec2(effectGap, 0), animalLastPosition)
                    break;
                case 270:
                    angle = 180
                    animalPosition.sub(new cc.Vec2(effectGap, 0), animalLastPosition)
                    break
                default:
                    break;
            }
            node.angle = angle

            let animalWorldPosition = this.node.parent.convertToWorldSpaceAR(animalLastPosition);
            node.setPosition(node.parent.convertToNodeSpaceAR(animalWorldPosition))

            cc.tween(node)
                .to(1.0, { opacity: 0 }) // 1秒内将透明度改为0
                .call(() => {
                    node.removeFromParent()
                    node.opacity = 255;
                    GameUI.inst.movementEffectPool.put(node);
                })
                .start();
        }, 0.2, 3, 0.2)

    }

    // 获取当前图集
    public getCurrentAtlas(): cc.SpriteAtlas {
        return this.atlas;
    }

    // 设置图集
    public setAtlas(atlas: cc.SpriteAtlas) {
        if (!atlas) {
            return;
        }

        // 如果原始图集还没加载完成，保存待处理的图集
        if (!this.isAtlasLoaded) {
            this.pendingColorSwitch = atlas;
            return;
        }

        this.atlas = atlas;
        this.changeCharacterAppearance();
    }

    // 获取当前图集名称
    public getCurrentAtlasName(): string {
        return this.atlas ? this.atlas.name : '';
    }

    // 检查图集是否已加载
    public isAtlasReady(): boolean {
        return this.isAtlasLoaded && this.atlas !== null;
    }

}
