// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import PrefabUtil from "../utils/manager/PrefabUtil";
import UIbase from "../utils/UIbase";
import mySkinData from "../datas/mySkinData";
import skinItemBtn from "../scripts/item/skinItemBtn";
import LocalData from "../manager/LocalData";

const {ccclass, property} = cc._decorator;

@ccclass
export default class SkinGalleryUI extends UIbase {

    @property(cc.Node)
    itemContainer:cc.Node = null;

    @property(cc.Prefab)
    itemPrefab:cc.Prefab = null;

    @property(cc.Label)
    collectionCountLabel:cc.Label = null;

    private static _inst:SkinGalleryUI;
    private currentSkin:skinItemBtn;

    public static get inst()
    {
        if(this._inst==null)
        {
            let prefab = PrefabUtil.get("SkinGalleryUI");
            if (!prefab) {
                console.error("SkinGalleryUI预制体未找到");
                return null;
            }
            let v=cc.instantiate(prefab);

            this._inst=v.getComponent(SkinGalleryUI);
        }

        return this._inst;
    }

    // LIFE-CYCLE CALLBACKS:
    protected onLoad(): void {
        // this.spawnItems()

        // this.node.on('selectSkin',(event:cc.Event.EventCustom)=>{
        //     if(this.currentSkin)
        //     {
        //         this.currentSkin.changeCheckMark();
        //     }
        //     if(event.getUserData()){
        //         this.currentSkin = event.getUserData();
        //     }
        // })

        cc.systemEvent.on('selectSkin',(script)=>{
            if(this.currentSkin)
            {
                this.currentSkin.changeCheckMark();
            }
            if(script){
                this.currentSkin = script;
            }
        })
    }

    protected start(): void {
        // this.spawnItems();
    }

    public initializeData(){
        this.itemContainer.removeAllChildren();
        this.spawnItems();
        this.updateLabel();
    }

    updateLabel() {
        this.collectionCountLabel.string = '收藏进度  ' + mySkinData.getCollectionProgress() + '/' + mySkinData.skins.length
    }

    public showUI(data?: any): void {
        super.showUI(data)
    }

    private spawnItems() {
        let skins = mySkinData.getSkins();

        for (let i = 0; i < skins.length; i++) {
            let item = cc.instantiate(this.itemPrefab);

            let btnScript:skinItemBtn = item.getComponent("skinItemBtn")
            btnScript.updateUi(skins[i])

            if(skins[i].id == LocalData.currentSkin){
                this.currentSkin = btnScript;
            }
            this.itemContainer.addChild(item);
        }
    }


    // update (dt) {}
}
