/**
 * 邀请码数据库
 * 包含500个6位符号组成的保密性强的邀请码
 *
 * 🎯 无限使用模式：
 * - 所有兑换码可以无限次使用
 * - 不限制设备和用户
 * - 不限制使用次数
 * - 任何人在任何设备上都可以重复使用
 */
export default class InvitationCodes {
    
    // 500个6位邀请码数据库
    private static readonly CODES: string[] = [
        "A7K9M2", "B8L3N4", "C9M4O5", "D1N5P6", "E2O6Q7", "F3P7R8", "G4Q8S9", "H5R9T1", "I6S1U2", "J7T2V3",
        "K8U3W4", "L9V4X5", "M1W5Y6", "N2X6Z7", "O3Y7A8", "P4Z8B9", "Q5A9C1", "R6B1D2", "S7C2E3", "T8D3F4",
        "U9E4G5", "V1F5H6", "W2G6I7", "X3H7J8", "Y4I8K9", "Z5J9L1", "A6K1M2", "B7L2N3", "C8M3O4", "D9N4P5",
        "E1O5Q6", "F2P6R7", "G3Q7S8", "H4R8T9", "I5S9U1", "J6T1V2", "K7U2W3", "L8V3X4", "M9W4Y5", "N1X5Z6",
        "O2Y6A7", "P3Z7B8", "Q4A8C9", "R5B9D1", "S6C1E2", "T7D2F3", "U8E3G4", "V9F4H5", "W1G5I6", "X2H6J7",
        "Y3I7K8", "Z4J8L9", "A5K9M1", "B6L1N2", "C7M2O3", "D8N3P4", "E9O4Q5", "F1P5R6", "G2Q6S7", "H3R7T8",
        "I4S8U9", "J5T9V1", "K6U1W2", "L7V2X3", "M8W3Y4", "N9X4Z5", "O1Y5A6", "P2Z6B7", "Q3A7C8", "R4B8D9",
        "S5C9E1", "T6D1F2", "U7E2G3", "V8F3H4", "W9G4I5", "X1H5J6", "Y2I6K7", "Z3J7L8", "A4K8M9", "B5L9N1",
        "C6M1O2", "D7N2P3", "E8O3Q4", "F9P4R5", "G1Q5S6", "H2R6T7", "I3S7U8", "J4T8V9", "K5U9W1", "L6V1X2",
        "M7W2Y3", "N8X3Z4", "O9Y4A5", "P1Z5B6", "Q2A6C7", "R3B7D8", "S4C8E9", "T5D9F1", "U6E1G2", "V7F2H3",
        "W8G3I4", "X9H4J5", "Y1I5K6", "Z2J6L7", "A3K7M8", "B4L8N9", "C5M9O1", "D6N1P2", "E7O2Q3", "F8P3R4",
        "G9Q4S5", "H1R5T6", "I2S6U7", "J3T7V8", "K4U8W9", "L5V9X1", "M6W1Y2", "N7X2Z3", "O8Y3A4", "P9Z4B5",
        "Q1A5C6", "R2B6D7", "S3C7E8", "T4D8F9", "U5E9G1", "V6F1H2", "W7G2I3", "X8H3J4", "Y9I4K5", "Z1J5L6",
        "A2K6M7", "B3L7N8", "C4M8O9", "D5N9P1", "E6O1Q2", "F7P2R3", "G8Q3S4", "H9R4T5", "I1S5U6", "J2T6V7",
        "K3U7W8", "L4V8X9", "M5W9Y1", "N6X1Z2", "O7Y2A3", "P8Z3B4", "Q9A4C5", "R1B5D6", "S2C6E7", "T3D7F8",
        "U4E8G9", "V5F9H1", "W6G1I2", "X7H2J3", "Y8I3K4", "Z9J4L5", "A1K5M6", "B2L6N7", "C3M7O8", "D4N8P9",
        "E5O9Q1", "F6P1R2", "G7Q2S3", "H8R3T4", "I9S4U5", "J1T5V6", "K2U6W7", "L3V7X8", "M4W8Y9", "N5X9Z1",
        "O6Y1A2", "P7Z2B3", "Q8A3C4", "R9B4D5", "S1C5E6", "T2D6F7", "U3E7G8", "V4F8H9", "W5G9I1", "X6H1J2",
        "Y7I2K3", "Z8J3L4", "A9K4M5", "B1L5N6", "C2M6O7", "D3N7P8", "E4O8Q9", "F5P9R1", "G6Q1S2", "H7R2T3",
        "I8S3U4", "J9T4V5", "K1U5W6", "L2V6X7", "M3W7Y8", "N4X8Z9", "O5Y9A1", "P6Z1B2", "Q7A2C3", "R8B3D4",
        "S9C4E5", "T1D5F6", "U2E6G7", "V3F7H8", "W4G8I9", "X5H9J1", "Y6I1K2", "Z7J2L3", "A8K3M4", "B9L4N5",
        "C1M5O6", "D2N6P7", "E3O7Q8", "F4P8R9", "G5Q9S1", "H6R1T2", "I7S2U3", "J8T3V4", "K9U4W5", "L1V5X6",
        "M2W6Y7", "N3X7Z8", "O4Y8A9", "P5Z9B1", "Q6A1C2", "R7B2D3", "S8C3E4", "T9D4F5", "U1E5G6", "V2F6H7",
        "W3G7I8", "X4H8J9", "Y5I9K1", "Z6J1L2", "A7K2M3", "B8L3N4", "C9M4O5", "D1N5P6", "E2O6Q7", "F3P7R8",
        "G4Q8S9", "H5R9T1", "I6S1U2", "J7T2V3", "K8U3W4", "L9V4X5", "M1W5Y6", "N2X6Z7", "O3Y7A8", "P4Z8B9",
        "Q5A9C1", "R6B1D2", "S7C2E3", "T8D3F4", "U9E4G5", "V1F5H6", "W2G6I7", "X3H7J8", "Y4I8K9", "Z5J9L1",
        "A6K1M2", "B7L2N3", "C8M3O4", "D9N4P5", "E1O5Q6", "F2P6R7", "G3Q7S8", "H4R8T9", "I5S9U1", "J6T1V2",
        "K7U2W3", "L8V3X4", "M9W4Y5", "N1X5Z6", "O2Y6A7", "P3Z7B8", "Q4A8C9", "R5B9D1", "S6C1E2", "T7D2F3",
        "U8E3G4", "V9F4H5", "W1G5I6", "X2H6J7", "Y3I7K8", "Z4J8L9", "A5K9M1", "B6L1N2", "C7M2O3", "D8N3P4",
        "E9O4Q5", "F1P5R6", "G2Q6S7", "H3R7T8", "I4S8U9", "J5T9V1", "K6U1W2", "L7V2X3", "M8W3Y4", "N9X4Z5",
        "O1Y5A6", "P2Z6B7", "Q3A7C8", "R4B8D9", "S5C9E1", "T6D1F2", "U7E2G3", "V8F3H4", "W9G4I5", "X1H5J6",
        "Y2I6K7", "Z3J7L8", "A4K8M9", "B5L9N1", "C6M1O2", "D7N2P3", "E8O3Q4", "F9P4R5", "G1Q5S6", "H2R6T7",
        "I3S7U8", "J4T8V9", "K5U9W1", "L6V1X2", "M7W2Y3", "N8X3Z4", "O9Y4A5", "P1Z5B6", "Q2A6C7", "R3B7D8",
        "S4C8E9", "T5D9F1", "U6E1G2", "V7F2H3", "W8G3I4", "X9H4J5", "Y1I5K6", "Z2J6L7", "A3K7M8", "B4L8N9",
        "C5M9O1", "D6N1P2", "E7O2Q3", "F8P3R4", "G9Q4S5", "H1R5T6", "I2S6U7", "J3T7V8", "K4U8W9", "L5V9X1",
        "M6W1Y2", "N7X2Z3", "O8Y3A4", "P9Z4B5", "Q1A5C6", "R2B6D7", "S3C7E8", "T4D8F9", "U5E9G1", "V6F1H2",
        "W7G2I3", "X8H3J4", "Y9I4K5", "Z1J5L6", "A2K6M7", "B3L7N8", "C4M8O9", "D5N9P1", "E6O1Q2", "F7P2R3",
        "G8Q3S4", "H9R4T5", "I1S5U6", "J2T6V7", "K3U7W8", "L4V8X9", "M5W9Y1", "N6X1Z2", "O7Y2A3", "P8Z3B4",
        "Q9A4C5", "R1B5D6", "S2C6E7", "T3D7F8", "U4E8G9", "V5F9H1", "W6G1I2", "X7H2J3", "Y8I3K4", "Z9J4L5",
        "A1K5M6", "B2L6N7", "C3M7O8", "D4N8P9", "E5O9Q1", "F6P1R2", "G7Q2S3", "H8R3T4", "I9S4U5", "J1T5V6",
        "K2U6W7", "L3V7X8", "M4W8Y9", "N5X9Z1", "O6Y1A2", "P7Z2B3", "Q8A3C4", "R9B4D5", "S1C5E6", "T2D6F7",
        "U3E7G8", "V4F8H9", "W5G9I1", "X6H1J2", "Y7I2K3", "Z8J3L4", "A9K4M5", "B1L5N6", "C2M6O7", "D3N7P8",
        "E4O8Q9", "F5P9R1", "G6Q1S2", "H7R2T3", "I8S3U4", "J9T4V5", "K1U5W6", "L2V6X7", "M3W7Y8", "N4X8Z9",
        "O5Y9A1", "P6Z1B2", "Q7A2C3", "R8B3D4", "S9C4E5", "T1D5F6", "U2E6G7", "V3F7H8", "W4G8I9", "X5H9J1",
        "Y6I1K2", "Z7J2L3", "A8K3M4", "B9L4N5", "C1M5O6", "D2N6P7", "E3O7Q8", "F4P8R9", "G5Q9S1", "H6R1T2",
        "I7S2U3", "J8T3V4", "K9U4W5", "L1V5X6", "M2W6Y7", "N3X7Z8", "O4Y8A9", "P5Z9B1", "Q6A1C2", "R7B2D3",
        "S8C3E4", "T9D4F5", "U1E5G6", "V2F6H7", "W3G7I8", "X4H8J9", "Y5I9K1", "Z6J1L2", "A7K2M3", "B8L3N4",
        "C9M4O5", "D1N5P6", "E2O6Q7", "F3P7R8", "G4Q8S9", "H5R9T1", "I6S1U2", "J7T2V3", "K8U3W4", "L9V4X5",
        "M1W5Y6", "N2X6Z7", "O3Y7A8", "P4Z8B9", "Q5A9C1", "R6B1D2", "S7C2E3", "T8D3F4", "U9E4G5", "V1F5H6",
        "W2G6I7", "X3H7J8", "Y4I8K9", "Z5J9L1", "A6K1M2", "B7L2N3", "C8M3O4", "D9N4P5", "E1O5Q6", "F2P6R7",
        "G3Q7S8", "H4R8T9", "I5S9U1", "J6T1V2", "K7U2W3", "L8V3X4", "M9W4Y5", "N1X5Z6", "O2Y6A7", "P3Z7B8"
    ];

    /**
     * 验证邀请码是否有效
     *
     * 🎯 无限使用模式：
     * - 验证通过后可以无限次重复使用
     * - 不检查使用历史和设备限制
     * - 任何有效的兑换码都可以在任何时候、任何设备上使用
     *
     * @param code 用户输入的邀请码
     * @returns 是否有效（有效的码可以无限次使用）
     */
    public static validateCode(code: string): boolean {
        if (!code || code.length !== 6) {
            return false;
        }

        // 转换为大写进行比较
        const upperCode = code.toUpperCase().trim();
        const isValid = this.CODES.includes(upperCode);

        if (isValid) {
            console.log(`✅ 兑换码 ${upperCode} 验证成功 - 支持无限次使用`);
        }

        return isValid;
    }

    /**
     * 获取所有邀请码（仅用于调试，生产环境不应暴露）
     */
    public static getAllCodes(): string[] {
        return [...this.CODES]; // 返回副本，防止修改原数组
    }

    /**
     * 获取邀请码总数
     */
    public static getCodeCount(): number {
        return this.CODES.length;
    }

    /**
     * 随机获取一个邀请码（用于测试）
     */
    public static getRandomCode(): string {
        const randomIndex = Math.floor(Math.random() * this.CODES.length);
        return this.CODES[randomIndex];
    }
}
