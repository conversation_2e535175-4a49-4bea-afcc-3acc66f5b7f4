// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { DataMgr } from "../manager/DataMgr";
import Plat from "../utils/Palt";
import PrefabUtil from "../utils/manager/PrefabUtil";
import ShopUI from "./ShopUI";

const { ccclass, property } = cc._decorator;

@ccclass
export default class ShopSkinItem extends cc.Component {

    @property(cc.Sprite)
    sprite: cc.Sprite = null;
    @property(cc.Node)
    lockNode: cc.Node = null;
    @property(cc.Node)
    useNode: cc.Node = null;
    @property(cc.Node)
    usingNode: cc.Node = null;
    @property(cc.Integer)
    id: number = 0
    setData() {
        cc.resources.load("/cat/cat_skin_" + this.id, cc.SpriteFrame, (error, frame) => {
            this.sprite.spriteFrame = frame
        })
        const isUnlock = DataMgr.ins.isUnlockSkin(this.id)
        this.usingNode.active = false
        this.useNode.active = false;
        this.lockNode.active = false;
        if (!isUnlock) {
            this.useNode.active = false;
            this.lockNode.active = true;
        } else {
            this.lockNode.active = false;
            const isuse = DataMgr.ins.isUseSkin(this.id);
            if (isuse) {
                this.useNode.active = false
                this.usingNode.active = true
            } else {
                this.useNode.active = true
                this.usingNode.active = false
            }
        }
    }

    onClickUnlock() {
        //todo ad
        Plat.showRewardVideo((success: boolean) => {
            if (success) {
                DataMgr.ins.unlockSkin(this.id)
                ShopUI.inst.updateNodesData()
            }
        })
    }

    onClickUse() {
        DataMgr.ins.setSkin(this.id)
        ShopUI.inst.updateNodesData()
    }
    // update (dt) {}
}
