const {ccclass, property} = cc._decorator;
import PrefabUtil from '../utils/manager/PrefabUtil';
import UIbase from '../utils/UIbase';


@ccclass
export default class TipsUI extends UIbase {

    private static _inst:TipsUI;

    public static get inst()
    {
        if(this._inst==null)
        {
            let prefab = PrefabUtil.get("TipsUI");
            if (!prefab) {
                console.error("TipsUI预制体未找到");
                return null;
            }
            let v=cc.instantiate(prefab);

            this._inst=v.getComponent(TipsUI);
        }

        return this._inst;
    }

    @property(cc.Label)
    tipsLabel:cc.Label=null;  //计时器

    private tw:cc.Tween;

    public showTips( str:string)
    {

        this.tipsLabel.string=str;

        this.showUI();

        this.node.zIndex=9999;
        this.node.x=cc.winSize.width/2;
        this.node.y=cc.winSize.height/2-40;
        this.node.opacity=0;


        if(this.tw ==null)
        {
            this.tw  = cc.tween(this.node)
            .to(0.3,{opacity:255,y:cc.winSize.height/2})
            .delay(1)
            .to(0.3,{opacity:0,y:cc.winSize.height/2+40})
            .call(()=>{
                this.hideUI();
            })
            .start();
        }
        else
        {
            this.tw.stop();
            this.tw.start();
        }
      
    }


}