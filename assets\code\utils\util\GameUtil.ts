export default class GameUtil
{

    public static randomRange(min,max)
    {
        return min+ Math.floor(Math.random()*(max-min));
    }

    
    public static randomRangeSort(arr)
    {
        for(var i = 0,len = arr.length;i < len; i++ )
        {
              var rand = GameUtil.randomRange(0,arr.length);             
              var temp = arr[rand];
              arr[rand] = arr[i];
              arr[i] = temp; 
        }
    }

    public static deleteValue(arr:Array<number>,val:number)
    {
        for(let i = 0;i<arr.length; i++ )
        {
            if(arr[i]==val)
            {
                arr.splice(i,1);
                return;
            }
        }
    }

    //获取年月日字符串
    // public static day_str() 
    // {
    //     var d = new Date();
    //     let str = '';
    //     str += d.getFullYear();
    //     str += d.getMonth() + 1;
    //     str += d.getDate();
    //     return str;
    // }
}