// Learn TypeScript:
import I18n from './I18n';
import executeInEditMode = cc._decorator.executeInEditMode;

const {ccclass, property} = cc._decorator;

@ccclass
@executeInEditMode()
export default class I18nLable extends cc.Component {

    private label: cc.Label | null = null;

    @property({ visible: false })
    key: string = '';
  
    // 声明property属性Key
    @property({ displayName: '翻译id', visible: true })
    get _key() {
      return this.key;
    }
    set _key(str: string) {
      this.key = str;
      this.updateLabel();
    }
  
    onLoad()
    { 
      this.fetchRender();
    }

    onEnable(): void {
      this.updateLabel();
    }
  
    public fetchRender () {
      // 获取文本Label组件
      let label = this.getComponent('cc.Label') as cc.Label;
      if (label) {
        this.label = label;
        this.updateLabel();
        return;
      } 
    }
  
    public updateLabel () {
      // 通过LanguageData的方法接口，根据语言类型和key获取指定的内容
      // 然后改变内容的显示
      this.label && (this.label.string = I18n.t(this.key));
    }

   
}

