import AnimalItem from "./AnimalItem";

const { ccclass, property } = cc._decorator;

/**
 * 动物换色过渡动画组件
 * 负责播放换色过渡动画效果
 */
@ccclass
export default class AnimalColorTransition extends cc.Component {

    @property({
        displayName: "换色过渡图集目录",
        tooltip: "换色过渡动画图集所在的目录路径"
    })
    transitionAtlasDirectory: string = "atlas/animals_transition/";

    private transitionAtlas: cc.SpriteAtlas = null;
    private frameCount: number = 10; // 换色动画帧数
    private isPlaying: boolean = false;

    /**
     * 播放换色过渡动画
     * @param animalItem 要换色的动物组件
     * @param targetAtlas 目标图集（换色后的图集）
     * @param onComplete 动画完成回调
     */
    public playColorTransition(animalItem: AnimalItem, targetAtlas: cc.SpriteAtlas, onComplete?: () => void) {
        if (this.isPlaying || !animalItem || !animalItem.isValid) {
            return;
        }

        this.isPlaying = true;

        // 获取动物类型，用于加载对应的换色过渡图集
        const animalType = this.getAnimalTypeFromAtlas(animalItem.getCurrentAtlasName());
        
        if (!animalType) {
            cc.warn("无法识别动物类型，跳过换色动画");
            this.completeTransition(animalItem, targetAtlas, onComplete);
            return;
        }

        // 加载换色过渡图集
        this.loadTransitionAtlas(animalType, (atlas) => {
            if (atlas) {
                this.playTransitionAnimation(animalItem, atlas, () => {
                    this.completeTransition(animalItem, targetAtlas, onComplete);
                });
            } else {
                // 如果没有过渡动画，直接切换
                this.completeTransition(animalItem, targetAtlas, onComplete);
            }
        });
    }

    /**
     * 从图集名称中提取动物类型
     */
    private getAnimalTypeFromAtlas(atlasName: string): string {
        if (!atlasName) return null;
        
        const cleanName = atlasName.replace('.plist', '').replace('_color', '');
        const animalTypes = ['cat', 'panda', 'pig', 'rabbit', 'sheep'];
        
        for (const type of animalTypes) {
            if (cleanName.includes(type)) {
                return type;
            }
        }
        
        return null;
    }

    /**
     * 加载换色过渡图集
     */
    private loadTransitionAtlas(animalType: string, callback: (atlas: cc.SpriteAtlas) => void) {
        const atlasPath = `${this.transitionAtlasDirectory}${animalType}_transition`;
        
        cc.resources.load(atlasPath, cc.SpriteAtlas, (error, atlas) => {
            if (error) {
                cc.warn(`换色过渡图集加载失败: ${atlasPath}`, error);
                callback(null);
            } else {
                this.transitionAtlas = atlas;
                callback(atlas);
            }
        });
    }

    /**
     * 播放过渡动画
     */
    private playTransitionAnimation(animalItem: AnimalItem, transitionAtlas: cc.SpriteAtlas, onComplete: () => void) {
        const animalNode = animalItem.animalNode;
        if (!animalNode) {
            onComplete();
            return;
        }

        let animation = animalNode.getComponent(cc.Animation);
        if (!animation) {
            onComplete();
            return;
        }

        // 停止当前动画
        animation.stop();

        // 创建过渡动画帧
        const transitionFrames = this.createTransitionSpriteFrames(transitionAtlas);
        
        if (transitionFrames.length === 0) {
            cc.warn("换色过渡帧创建失败");
            onComplete();
            return;
        }

        // 清除现有的过渡动画剪辑
        animation.removeClip("anim_color_transition");

        // 创建过渡动画剪辑
        const transitionClip = cc.AnimationClip.createWithSpriteFrames(transitionFrames, 6); // 6fps - 统一动画速度
        transitionClip.name = "anim_color_transition";
        transitionClip.wrapMode = cc.WrapMode.Normal; // 只播放一次
        transitionClip.speed = 1;

        animation.addClip(transitionClip);

        // 监听动画完成事件
        const onAnimationComplete = (type: string, state: cc.AnimationState) => {
            if (state.name === "anim_color_transition") {
                animation.off('stop', onAnimationComplete, this);
                onComplete();
            }
        };

        animation.on('stop', onAnimationComplete, this);
        animation.play('anim_color_transition');
    }

    /**
     * 创建过渡动画的精灵帧
     */
    private createTransitionSpriteFrames(atlas: cc.SpriteAtlas): cc.SpriteFrame[] {
        const frames: cc.SpriteFrame[] = [];
        
        for (let i = 1; i <= this.frameCount; i++) {
            // 尝试多种帧名称格式
            const frameNames = [
                i.toString(),           // "1", "2", "3"...
                `${i}.PNG`,            // "1.PNG", "2.PNG", "3.PNG"...
                `${i}.png`             // "1.png", "2.png", "3.png"...
            ];

            let spriteFrame: cc.SpriteFrame = null;
            for (const frameName of frameNames) {
                spriteFrame = atlas.getSpriteFrame(frameName);
                if (spriteFrame) {
                    break;
                }
            }

            if (spriteFrame) {
                frames.push(spriteFrame);
            }
        }
        
        return frames;
    }

    /**
     * 完成换色过渡
     */
    private completeTransition(animalItem: AnimalItem, targetAtlas: cc.SpriteAtlas, onComplete?: () => void) {
        this.isPlaying = false;
        
        // 切换到目标图集
        if (targetAtlas) {
            animalItem.setAtlas(targetAtlas);
        }
        
        // 调用完成回调
        if (onComplete) {
            onComplete();
        }
    }

    /**
     * 检查是否正在播放换色动画
     */
    public isTransitionPlaying(): boolean {
        return this.isPlaying;
    }
}
