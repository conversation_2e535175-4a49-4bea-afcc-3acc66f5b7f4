// Created by carolsail
import UIbase from '../utils/UIbase';
import PrefabUtil from '../utils/manager/PrefabUtil';
import AudioMgr from '../manager/AudioMgr';
import AudioPath from '../datas/AudioPath';
import Game<PERSON> from './GameUI';
import Plat from '../utils/Palt';
import { BuffItems } from '../datas/Constants';
import LocalData from '../manager/LocalData';
import buffItemBtn from '../scripts/buffItemBtn';

const { ccclass, property } = cc._decorator;

@ccclass
export default class BuffItemUI extends UIbase {

    @property(cc.Label)
    buffItemName: cc.Label = null;

    @property(cc.Sprite)
    buffItemSprite: cc.Sprite = null;

    @property(cc.RichText)
    buffItemLabel: cc.RichText = null;

    private currentBuffItem:BuffItems = null;

    private static _inst: BuffItemUI;

    @property
    _buffItemBtn = null;

    public static get inst() {
        if (this._inst == null || this._inst.node == null) {
            let v = cc.instantiate(PrefabUtil.get("BuffItemUI"));

            this._inst = v.getComponent(BuffItemUI);
        }

        return this._inst;
    }

    start() {
    }

    public showUI(script: buffItemBtn): void {
        // if (this.buffItemName) {
        //     switch (script.identifier) {
        //         case BuffItems.AddTime:
        //             this.buffItemName.string = '加时'
        //             break;
        //             case BuffItems.Hint:
        //             this.buffItemName.string = '提示'
        //             break;
        //             case BuffItems.Inverse:
        //             this.buffItemName.string = '反转'
        //             break;
        //             case BuffItems.SwapAnimal:
        //             this.buffItemName.string = '斯瓦普'
        //             break;
        //             case BuffItems.Undo:
        //             this.buffItemName.string = '撤销'
        //             break;
            
        //         default:
        //             break;
        //     }
        this._buffItemBtn = script;
        this.currentBuffItem = script.identifier;
        this.onClickAdReward()
        // this.updateLabel(script.identifier);
            // this.updateSpriteFrame(script.identifier);
            // super.showUI()
        // }
    }
    updateSpriteFrame(name: BuffItems) {
        if (this.buffItemSprite) {
            let fileName = '';
            switch (name) {
                case BuffItems.Undo:
                    fileName = 'btn_undo'
                    break;
                case BuffItems.Inverse:
                    fileName = 'btn_inverse'
                    break;
                case BuffItems.Hint:
                    fileName = 'btn_hint'
                    break;
                case BuffItems.SwapAnimal:
                    fileName = 'btn_fluctuate'
                    break;
                case BuffItems.AddTime:
                    fileName = 'btn_addTime'
                    break;

                default:
                    break;
            }

            cc.resources.load('/other/' + fileName, cc.SpriteFrame, (error, frame) => {
                if (error) {
                    console.log(error.message)
                }else{
                    this.buffItemSprite.spriteFrame = frame
                }
            })
        }
    }

    updateLabel(name: BuffItems) {
        if (this.buffItemLabel) {
            let string = '';
            let string2 = '';
            switch (name) {
                case BuffItems.Undo:
                    string = '允许玩家在游戏中回到上一步的状态，撤销最近的一次操作。'
                    string2 = '"玩家表示非常后悔"。'
                    break;
                case BuffItems.Inverse:
                    string = '这个道具会改变动物角色的朝向.'
                    string2 = '"有时候不是我们不努力，只是方向错了。"'
                    break;
                case BuffItems.Hint:
                    string = '这个道具会在游戏中给出提示，显示当前可以进行操作的动物。'
                    string2 = '"人生啊有时候就是缺少一点点提示。"'
                    break;
                case BuffItems.SwapAnimal:
                    string = '这个道具会随机更换游戏中的动物.'
                    string2 = '"也许看起来更舒服一些？"'
                    break;
                case BuffItems.AddTime:
                    string = '这个道具会增加游戏的剩余时间.'
                    string2 = '"又有时间思考了！"'
                    break;

                default:
                    break;
            }

            this.buffItemLabel.string = `${string}<br/><b>${string2}</b>`

        }
    }

    protected onShow(): void {
        // Plat.showBanner()
    }

    protected onHide(): void {
        // Plat.hideBanner()
    }

    onClickClose() {
        AudioMgr.playSound(AudioPath.CLICK)
        GameUI.inst.is_game_pause = false;
        this.hideUI();
    }

    onClickAdReward() {
        Plat.showRewardVideo((state: Boolean) => {
            if (state) {
                 GameUI.inst.useBuffItem(this._buffItemBtn)

                this._buffItemBtn.init()
            }
            GameUI.inst.is_game_pause = false;
            // this.hideUI();
        })
    }


}
