import LocalData from "../manager/LocalData";

export default class provincesData {
    public static provinces: { [key: string]: { name: string; score: number } } =
        {
            "anhui": {
                "name": "安徽",
                "score": 2143
            },
            "beijing": {
                "name": "北京",
                "score": 2089
            },
            "chongqing": {
                "name": "重庆",
                "score": 1935
            },
            "fujian": {
                "name": "福建",
                "score": 2105
            },
            "gansu": {
                "name": "甘肃",
                "score": 2122
            },
            "guangdong": {
                "name": "广东",
                "score": 1912
            },
            "guangxi": {
                "name": "广西",
                "score": 2070
            },
            "guizhou": {
                "name": "贵州",
                "score": 1932
            },
            "hainan": {
                "name": "海南",
                "score": 2087
            },
            "hebei": {
                "name": "河北",
                "score": 2069
            },
            "heilongjiang": {
                "name": "黑龙江",
                "score": 2145
            },
            "henan": {
                "name": "河南",
                "score": 2056
            },
            "hubei": {
                "name": "湖北",
                "score": 2023
            },
            "hunan": {
                "name": "湖南",
                "score": 2135
            },
            "jiangsu": {
                "name": "江苏",
                "score": 2186
            },
            "jiangxi": {
                "name": "江西",
                "score": 2082
            },
            "jilin": {
                "name": "吉林",
                "score": 1904
            },
            "liaoning": {
                "name": "辽宁",
                "score": 2085
            },
            "neimenggu": {
                "name": "内蒙古",
                "score": 2115
            },
            "ningxia": {
                "name": "宁夏",
                "score": 2121
            },
            "qinghai": {
                "name": "青海",
                "score": 2200
            },
            "shandong": {
                "name": "山东",
                "score": 2047
            },
            "shanghai": {
                "name": "上海",
                "score": 1942
            },
            "shanxi": {
                "name": "山西",
                "score": 2160
            },
            "shanxi2": {
                "name": "陕西",
                "score": 2068
            },
            "sichuan": {
                "name": "四川",
                "score": 1947
            },
            "tianjin": {
                "name": "天津",
                "score": 2130
            },
            "xinjiang": {
                "name": "新疆",
                "score": 2102
            },
            "xizang": {
                "name": "西藏",
                "score": 2058
            },
            "yunnan": {
                "name": "云南",
                "score": 2180
            },
            "zhejiang": {
                "name": "浙江",
                "score": 2022
            },
            "hongkong": {
                "name": "香港",
                "score": 198
            },
            "macau": {
                "name": "澳门",
                "score": 210
            },
            "taiwan": {
                "name": "台湾",
                "score": 29
            }
        }

    public static localDataName =   'provinceData'
    /**
     * static getProvinceRank
     */
    public static getProvinceRank(): Array<{ id: string, name: string, score: number }> {

        // 将对象转换为数组
        const provinceArray = Object.keys(this.provinces).map(key => ({
            id: key,
            name: this.provinces[key].name,
            score: this.provinces[key].score
        }));

        // 按照 score 属性从大到小排序
        provinceArray.sort((a, b) => b.score - a.score);

        return provinceArray;
    }

    public static findUserProvincePercentage(): number {
        let array = this.getProvinceRank();
        let targetId = LocalData.userRegional;

        if (targetId == '') {
            return 1;
        }
        const index = array.findIndex(element => element.id === targetId);

        if (index === -1) {
            console.log("The user's province is not in the data")
            return 1;
        }

        const totalElements = array.length;

        const percentage = (totalElements - index) / totalElements;

        return percentage;
    }

    public static generateRegionTeamScores() {

        // 获取当前时间
        const currentTime = Date.now();

        // 检查是否经过了周一的零点
        if (this.hasPassedMondayMidnight(currentTime)) {

            this.initProvinceData()

        } else {
            if(LocalData.getItemToStr('provinceData') != '')
            {
                try {

                    this.provinces =  JSON.parse(LocalData.getItemToStr(this.localDataName));
                  
                } catch (error) {
                    console.log(error)

                }

                this.increaseRegionTeamScoresByPercentage()
            }else{
                this.initProvinceData()
            }

        }

        // 更新上次运行时间
        LocalData.setItemToNumber('lastRunTime', currentTime)
    }


    public static increaseRegionTeamScoresByPercentage() {

        for (const key in this.provinces) {
            if (this.provinces.hasOwnProperty(key)) {
                this.provinces[key].score = this.incrementValue(this.provinces[key].score)
            }
        }
        LocalData.setItemToStr(this.localDataName, JSON.stringify(this.provinces));
    }

    public static initProvinceData() {

        for (const key in this.provinces) {
            if (this.provinces.hasOwnProperty(key)) {
                this.provinces[key].score = Math.floor(Math.random() * (1000 - 500 + 1)) + 500;
            }
        }
        LocalData.setItemToStr(this.localDataName, JSON.stringify(this.provinces));
    }

    //提高1%-3%
    public static incrementValue(value: number): number {
        const incrementPercentage = Math.random() * (3 - 1) + 1;
        return Math.floor(value * (1 + incrementPercentage / 100));
    }


    public static hasPassedMondayMidnight(currentTime: number): boolean {
        const currentDate = new Date(currentTime);

        if(LocalData.getItemToNumber('lastRunTime') == 0)
        {
            LocalData.setItemToNumber('lastRunTime', currentTime)

            return true;
        }

        const lastRunDate = new Date(LocalData.getItemToNumber('lastRunTime'));

        // 计算两个时间点之间的时间差（以毫秒为单位）
        const timeDifference = currentDate.getTime() - lastRunDate.getTime();

        // 计算两个时间点之间的天数差
        const daysDifference = Math.floor(timeDifference / (24 * 60 * 60 * 1000));

        // 检查是否经过了周一的零点
        if (daysDifference >= 7) {
            // 如果时间差超过一周，肯定经过了周一的零点
            return true;
        } else {
            // 计算上次运行时间的周一零点时间
            const lastMondayMidnight = new Date(lastRunDate);
            lastMondayMidnight.setDate(lastRunDate.getDate() - lastRunDate.getDay() + 1);
            lastMondayMidnight.setHours(0, 0, 0, 0);

            // 计算当前时间的周一零点时间
            const currentMondayMidnight = new Date(currentDate);
            currentMondayMidnight.setDate(currentDate.getDate() - currentDate.getDay() + 1);
            currentMondayMidnight.setHours(0, 0, 0, 0);

            // 如果当前时间的周一零点时间大于上次运行时间的周一零点时间，说明经过了周一的零点
            return currentMondayMidnight.getTime() > lastMondayMidnight.getTime();
        }
    }
}